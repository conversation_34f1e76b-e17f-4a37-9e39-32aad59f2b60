<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('admin');

$event_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$event_type = isset($_GET['type']) ? $_GET['type'] : '';

if (!$event_id || !$event_type) {
    echo '<div class="alert alert-danger">معرف الحدث غير صحيح</div>';
    exit;
}

if ($event_type === 'session') {
    // Get session details
    $stmt = $conn->prepare("
        SELECT cs.*, c.title as course_title, c.description as course_description,
               u.name as instructor_name, u.email as instructor_email,
               COUNT(DISTINCT e.user_id) as enrolled_students,
               COUNT(DISTINCT a.user_id) as attended_students
        FROM course_sessions cs
        JOIN courses c ON cs.course_id = c.id
        LEFT JOIN users u ON c.instructor_id = u.id
        LEFT JOIN enrollments e ON c.id = e.course_id AND e.status IN ('active', 'completed')
        LEFT JOIN attendance a ON cs.id = a.session_id AND a.status = 'present'
        WHERE cs.id = ?
        GROUP BY cs.id
    ");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        echo '<div class="alert alert-danger">الجلسة غير موجودة</div>';
        exit;
    }
    
    // Get attendance list
    $attendance_stmt = $conn->prepare("
        SELECT u.name, u.email, a.status, a.check_in_time, a.check_out_time
        FROM enrollments e
        JOIN users u ON e.user_id = u.id
        LEFT JOIN attendance a ON e.user_id = a.user_id AND a.session_id = ?
        WHERE e.course_id = ? AND e.status IN ('active', 'completed')
        ORDER BY u.name
    ");
    $attendance_stmt->execute([$event_id, $event['course_id']]);
    $attendance = $attendance_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    ?>
    <div class="row">
        <div class="col-md-8">
            <h4 class="text-primary mb-3">
                <i class="fas fa-calendar-alt me-2"></i>
                <?php echo htmlspecialchars($event['title']); ?>
            </h4>
            
            <div class="mb-3">
                <h6>الدورة:</h6>
                <p class="mb-1"><?php echo htmlspecialchars($event['course_title']); ?></p>
                <?php if ($event['course_description']): ?>
                    <small class="text-muted"><?php echo htmlspecialchars($event['course_description']); ?></small>
                <?php endif; ?>
            </div>
            
            <?php if ($event['description']): ?>
                <div class="mb-3">
                    <h6>وصف الجلسة:</h6>
                    <p><?php echo nl2br(htmlspecialchars($event['description'])); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-sm-6 mb-3">
                    <h6>التاريخ والوقت:</h6>
                    <p class="mb-1">
                        <i class="fas fa-calendar me-2"></i>
                        <?php echo date('d/m/Y', strtotime($event['session_date'])); ?>
                    </p>
                    <p class="mb-1">
                        <i class="fas fa-clock me-2"></i>
                        <?php echo date('H:i', strtotime($event['start_time'])); ?> - 
                        <?php echo date('H:i', strtotime($event['end_time'])); ?>
                    </p>
                </div>
                <div class="col-sm-6 mb-3">
                    <h6>المكان:</h6>
                    <p>
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <?php echo htmlspecialchars($event['location'] ?? 'غير محدد'); ?>
                    </p>
                </div>
            </div>
            
            <div class="mb-3">
                <h6>المدرب:</h6>
                <p class="mb-1"><?php echo htmlspecialchars($event['instructor_name'] ?? 'غير محدد'); ?></p>
                <?php if ($event['instructor_email']): ?>
                    <small class="text-muted"><?php echo htmlspecialchars($event['instructor_email']); ?></small>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">إحصائيات الحضور</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary"><?php echo $event['enrolled_students']; ?></h4>
                            <small>المسجلين</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?php echo $event['attended_students']; ?></h4>
                            <small>الحاضرين</small>
                        </div>
                    </div>
                    <?php if ($event['enrolled_students'] > 0): ?>
                        <div class="mt-3">
                            <div class="progress">
                                <div class="progress-bar" style="width: <?php echo round(($event['attended_students'] / $event['enrolled_students']) * 100); ?>%"></div>
                            </div>
                            <small class="text-muted">معدل الحضور: <?php echo round(($event['attended_students'] / $event['enrolled_students']) * 100); ?>%</small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="mt-3">
                <a href="session-edit.php?id=<?php echo $event['id']; ?>" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-edit me-2"></i>تعديل الجلسة
                </a>
                <a href="attendance.php?session_id=<?php echo $event['id']; ?>" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-check me-2"></i>إدارة الحضور
                </a>
            </div>
        </div>
    </div>
    
    <!-- Attendance List -->
    <div class="mt-4">
        <h6>قائمة الحضور:</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead class="table-light">
                    <tr>
                        <th>الطالب</th>
                        <th>البريد الإلكتروني</th>
                        <th>الحالة</th>
                        <th>وقت الدخول</th>
                        <th>وقت الخروج</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($attendance as $student): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($student['name']); ?></td>
                            <td><?php echo htmlspecialchars($student['email']); ?></td>
                            <td>
                                <?php if ($student['status']): ?>
                                    <span class="badge bg-<?php 
                                        echo match($student['status']) {
                                            'present' => 'success',
                                            'late' => 'warning',
                                            'absent' => 'danger',
                                            'excused' => 'info',
                                            default => 'secondary'
                                        };
                                    ?>">
                                        <?php 
                                        echo match($student['status']) {
                                            'present' => 'حاضر',
                                            'late' => 'متأخر',
                                            'absent' => 'غائب',
                                            'excused' => 'معذور',
                                            default => 'غير محدد'
                                        };
                                        ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير مسجل</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo $student['check_in_time'] ? date('H:i', strtotime($student['check_in_time'])) : '-'; ?></td>
                            <td><?php echo $student['check_out_time'] ? date('H:i', strtotime($student['check_out_time'])) : '-'; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php
    
} elseif ($event_type === 'course_start') {
    // Get course details
    $stmt = $conn->prepare("
        SELECT c.*, cat.name as category_name,
               u.name as instructor_name, u.email as instructor_email, u.bio as instructor_bio,
               COUNT(DISTINCT e.user_id) as enrolled_students
        FROM courses c
        LEFT JOIN categories cat ON c.category_id = cat.id
        LEFT JOIN users u ON c.instructor_id = u.id
        LEFT JOIN enrollments e ON c.id = e.course_id AND e.status IN ('active', 'completed')
        WHERE c.id = ?
        GROUP BY c.id
    ");
    $stmt->execute([$event_id]);
    $course = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$course) {
        echo '<div class="alert alert-danger">الدورة غير موجودة</div>';
        exit;
    }
    
    ?>
    <div class="row">
        <div class="col-md-8">
            <h4 class="text-warning mb-3">
                <i class="fas fa-star me-2"></i>
                بداية دورة: <?php echo htmlspecialchars($course['title']); ?>
            </h4>
            
            <div class="mb-3">
                <h6>وصف الدورة:</h6>
                <p><?php echo nl2br(htmlspecialchars($course['description'])); ?></p>
            </div>
            
            <?php if ($course['objectives']): ?>
                <div class="mb-3">
                    <h6>أهداف الدورة:</h6>
                    <p><?php echo nl2br(htmlspecialchars($course['objectives'])); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-sm-6 mb-3">
                    <h6>تاريخ البداية:</h6>
                    <p>
                        <i class="fas fa-calendar me-2"></i>
                        <?php echo date('d/m/Y', strtotime($course['start_date'])); ?>
                    </p>
                </div>
                <div class="col-sm-6 mb-3">
                    <h6>تاريخ النهاية:</h6>
                    <p>
                        <i class="fas fa-calendar me-2"></i>
                        <?php echo $course['end_date'] ? date('d/m/Y', strtotime($course['end_date'])) : 'غير محدد'; ?>
                    </p>
                </div>
            </div>
            
            <div class="mb-3">
                <h6>المدرب:</h6>
                <p class="mb-1"><?php echo htmlspecialchars($course['instructor_name'] ?? 'غير محدد'); ?></p>
                <?php if ($course['instructor_email']): ?>
                    <small class="text-muted"><?php echo htmlspecialchars($course['instructor_email']); ?></small>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">معلومات الدورة</h6>
                </div>
                <div class="card-body">
                    <p><strong>التصنيف:</strong> <?php echo htmlspecialchars($course['category_name'] ?? 'غير محدد'); ?></p>
                    <p><strong>المستوى:</strong> <?php echo htmlspecialchars($course['level'] ?? 'غير محدد'); ?></p>
                    <p><strong>المدة:</strong> <?php echo $course['duration_hours']; ?> ساعة</p>
                    <p><strong>السعر:</strong> <?php echo $course['price'] > 0 ? number_format($course['price']) . ' ريال' : 'مجانية'; ?></p>
                    <p><strong>الطلاب المسجلين:</strong> <?php echo $course['enrolled_students']; ?></p>
                </div>
            </div>
            
            <div class="mt-3">
                <a href="course-edit.php?id=<?php echo $course['id']; ?>" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-edit me-2"></i>تعديل الدورة
                </a>
                <a href="course-manage.php?id=<?php echo $course['id']; ?>" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-cog me-2"></i>إدارة الدورة
                </a>
            </div>
        </div>
    </div>
    <?php
}
?>
