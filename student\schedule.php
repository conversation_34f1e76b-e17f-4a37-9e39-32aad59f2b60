<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('student');

$user_id = $_SESSION['user_id'];

// Get student's enrolled courses with upcoming sessions
$sessions_stmt = $conn->prepare("
    SELECT cs.*, c.title as course_title, c.instructor_id,
           u.name as instructor_name,
           e.status as enrollment_status,
           a.status as attendance_status
    FROM course_sessions cs
    JOIN courses c ON cs.course_id = c.id
    JOIN enrollments e ON c.id = e.course_id AND e.user_id = ?
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN attendance a ON cs.id = a.session_id AND a.user_id = ?
    WHERE e.status IN ('active', 'completed')
    AND cs.session_date >= CURDATE()
    ORDER BY cs.session_date ASC, cs.start_time ASC
");
$sessions_stmt->execute([$user_id, $user_id]);
$upcoming_sessions = $sessions_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get past sessions for history
$past_sessions_stmt = $conn->prepare("
    SELECT cs.*, c.title as course_title, c.instructor_id,
           u.name as instructor_name,
           a.status as attendance_status,
           a.check_in_time, a.check_out_time
    FROM course_sessions cs
    JOIN courses c ON cs.course_id = c.id
    JOIN enrollments e ON c.id = e.course_id AND e.user_id = ?
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN attendance a ON cs.id = a.session_id AND a.user_id = ?
    WHERE e.status IN ('active', 'completed')
    AND cs.session_date < CURDATE()
    ORDER BY cs.session_date DESC, cs.start_time DESC
    LIMIT 20
");
$past_sessions_stmt->execute([$user_id, $user_id]);
$past_sessions = $past_sessions_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get today's sessions
$today_sessions = array_filter($upcoming_sessions, function($session) {
    return date('Y-m-d', strtotime($session['session_date'])) === date('Y-m-d');
});

$page_title = 'جدولي الدراسي';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                دوراتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="schedule.php">
                                <i class="fas fa-calendar-alt"></i>
                                جدولي الدراسي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="progress.php">
                                <i class="fas fa-chart-line"></i>
                                تقدمي الدراسي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i>
                                شهاداتي
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">جدولي الدراسي</h1>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="exportSchedule()">
                            <i class="fas fa-download me-2"></i>تصدير الجدول
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="location.reload()">
                            <i class="fas fa-sync-alt me-2"></i>تحديث
                        </button>
                    </div>
                </div>

                <!-- Today's Sessions -->
                <?php if (!empty($today_sessions)): ?>
                    <div class="card mb-4 border-primary">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                جلسات اليوم - <?php echo date('d/m/Y'); ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <?php foreach ($today_sessions as $session): ?>
                                    <div class="col-md-6">
                                        <div class="card border-0 bg-light">
                                            <div class="card-body">
                                                <h6 class="card-title fw-bold"><?php echo htmlspecialchars($session['course_title']); ?></h6>
                                                <p class="card-text mb-2">
                                                    <strong><?php echo htmlspecialchars($session['title']); ?></strong>
                                                </p>
                                                <div class="session-details">
                                                    <div class="mb-1">
                                                        <i class="fas fa-clock me-2 text-primary"></i>
                                                        <?php echo date('H:i', strtotime($session['start_time'])); ?> - 
                                                        <?php echo date('H:i', strtotime($session['end_time'])); ?>
                                                    </div>
                                                    <?php if ($session['location']): ?>
                                                        <div class="mb-1">
                                                            <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                                            <?php echo htmlspecialchars($session['location']); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                    <?php if ($session['instructor_name']): ?>
                                                        <div class="mb-1">
                                                            <i class="fas fa-user-tie me-2 text-primary"></i>
                                                            <?php echo htmlspecialchars($session['instructor_name']); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <?php if ($session['attendance_status']): ?>
                                                    <span class="badge bg-<?php echo $session['attendance_status'] === 'present' ? 'success' : 'warning'; ?>">
                                                        <?php echo $session['attendance_status'] === 'present' ? 'حاضر' : 'غائب'; ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Upcoming Sessions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-plus me-2"></i>
                            الجلسات القادمة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($upcoming_sessions)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h5>لا توجد جلسات قادمة</h5>
                                <p class="text-muted">لا توجد جلسات مجدولة في الوقت الحالي</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الوقت</th>
                                            <th>الدورة</th>
                                            <th>الجلسة</th>
                                            <th>المدرب</th>
                                            <th>المكان</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($upcoming_sessions as $session): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo date('d/m/Y', strtotime($session['session_date'])); ?></strong>
                                                    <br><small class="text-muted"><?php echo date('l', strtotime($session['session_date'])); ?></small>
                                                </td>
                                                <td>
                                                    <?php echo date('H:i', strtotime($session['start_time'])); ?> - 
                                                    <?php echo date('H:i', strtotime($session['end_time'])); ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($session['course_title']); ?></strong>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($session['title']); ?>
                                                    <?php if ($session['description']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($session['description']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($session['instructor_name'] ?? 'غير محدد'); ?></td>
                                                <td><?php echo htmlspecialchars($session['location'] ?? 'غير محدد'); ?></td>
                                                <td>
                                                    <?php if ($session['attendance_status']): ?>
                                                        <span class="badge bg-<?php echo $session['attendance_status'] === 'present' ? 'success' : 'warning'; ?>">
                                                            <?php echo $session['attendance_status'] === 'present' ? 'حاضر' : 'غائب'; ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">لم يتم التسجيل</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Past Sessions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            الجلسات السابقة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($past_sessions)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                <h5>لا توجد جلسات سابقة</h5>
                                <p class="text-muted">لم تحضر أي جلسات بعد</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الدورة</th>
                                            <th>الجلسة</th>
                                            <th>المدرب</th>
                                            <th>الحضور</th>
                                            <th>وقت الدخول</th>
                                            <th>وقت الخروج</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($past_sessions as $session): ?>
                                            <tr>
                                                <td><?php echo date('d/m/Y', strtotime($session['session_date'])); ?></td>
                                                <td><?php echo htmlspecialchars($session['course_title']); ?></td>
                                                <td><?php echo htmlspecialchars($session['title']); ?></td>
                                                <td><?php echo htmlspecialchars($session['instructor_name'] ?? 'غير محدد'); ?></td>
                                                <td>
                                                    <?php if ($session['attendance_status']): ?>
                                                        <span class="badge bg-<?php 
                                                            echo match($session['attendance_status']) {
                                                                'present' => 'success',
                                                                'late' => 'warning',
                                                                'absent' => 'danger',
                                                                'excused' => 'info',
                                                                default => 'secondary'
                                                            };
                                                        ?>">
                                                            <?php 
                                                            echo match($session['attendance_status']) {
                                                                'present' => 'حاضر',
                                                                'late' => 'متأخر',
                                                                'absent' => 'غائب',
                                                                'excused' => 'معذور',
                                                                default => 'غير محدد'
                                                            };
                                                            ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">غير مسجل</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php echo $session['check_in_time'] ? date('H:i', strtotime($session['check_in_time'])) : '-'; ?>
                                                </td>
                                                <td>
                                                    <?php echo $session['check_out_time'] ? date('H:i', strtotime($session['check_out_time'])) : '-'; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
function exportSchedule() {
    // Create a simple CSV export
    let csv = 'التاريخ,الوقت,الدورة,الجلسة,المدرب,المكان\n';
    
    <?php foreach ($upcoming_sessions as $session): ?>
        csv += '<?php echo date("d/m/Y", strtotime($session["session_date"])); ?>,';
        csv += '<?php echo date("H:i", strtotime($session["start_time"])) . "-" . date("H:i", strtotime($session["end_time"])); ?>,';
        csv += '<?php echo addslashes($session["course_title"]); ?>,';
        csv += '<?php echo addslashes($session["title"]); ?>,';
        csv += '<?php echo addslashes($session["instructor_name"] ?? "غير محدد"); ?>,';
        csv += '<?php echo addslashes($session["location"] ?? "غير محدد"); ?>\n';
    <?php endforeach; ?>
    
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'جدولي_الدراسي.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Auto-refresh every 5 minutes
setInterval(() => {
    location.reload();
}, 300000);
</script>

<?php include '../includes/footer.php'; ?>
