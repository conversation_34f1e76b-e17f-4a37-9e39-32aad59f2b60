<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('student');

$user_id = $_SESSION['user_id'];

// Get completed courses with certificates
$certificates_stmt = $conn->prepare("
    SELECT e.*, c.title as course_title, c.duration_hours, c.instructor_id,
           u.name as instructor_name, e.completion_date,
           (SELECT AVG(score) FROM evaluations ev WHERE ev.user_id = e.user_id AND ev.course_id = e.course_id AND ev.score IS NOT NULL) as avg_score
    FROM enrollments e 
    JOIN courses c ON e.course_id = c.id 
    LEFT JOIN users u ON c.instructor_id = u.id
    WHERE e.user_id = ? AND e.status = 'completed' 
    ORDER BY e.completion_date DESC
");
$certificates_stmt->execute([$user_id]);
$certificates = $certificates_stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle certificate generation
if (isset($_GET['generate']) && isset($_GET['enrollment_id'])) {
    $enrollment_id = intval($_GET['enrollment_id']);
    
    // Verify enrollment belongs to user and is completed
    $verify_stmt = $conn->prepare("
        SELECT e.*, c.title as course_title, c.duration_hours, u.name as instructor_name 
        FROM enrollments e 
        JOIN courses c ON e.course_id = c.id 
        LEFT JOIN users u ON c.instructor_id = u.id
        WHERE e.id = ? AND e.user_id = ? AND e.status = 'completed'
    ");
    $verify_stmt->execute([$enrollment_id, $user_id]);
    $enrollment = $verify_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($enrollment) {
        // Generate certificate (this would typically create a PDF)
        generateCertificate($enrollment, $_SESSION['user_name']);
        exit;
    }
}

$page_title = 'شهاداتي';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                دوراتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="browse-courses.php">
                                <i class="fas fa-search"></i>
                                تصفح الدورات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="certificates.php">
                                <i class="fas fa-certificate"></i>
                                شهاداتي
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">شهاداتي</h1>
                </div>

                <?php if (empty($certificates)): ?>
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-certificate fa-4x text-muted mb-4"></i>
                            <h4>لا توجد شهادات بعد</h4>
                            <p class="text-muted mb-4">
                                أكمل الدورات للحصول على شهادات إتمام معتمدة
                            </p>
                            <a href="browse-courses.php" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                تصفح الدورات
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="row g-4">
                        <?php foreach ($certificates as $cert): ?>
                            <div class="col-lg-6 col-xl-4">
                                <div class="card certificate-card h-100 border-0 shadow-sm">
                                    <div class="card-header bg-gradient-primary text-white text-center">
                                        <i class="fas fa-certificate fa-2x mb-2"></i>
                                        <h6 class="mb-0">شهادة إتمام</h6>
                                    </div>
                                    <div class="card-body">
                                        <h5 class="card-title fw-bold mb-3">
                                            <?php echo htmlspecialchars($cert['course_title']); ?>
                                        </h5>
                                        
                                        <div class="certificate-details">
                                            <div class="detail-item mb-2">
                                                <i class="fas fa-calendar text-primary me-2"></i>
                                                <strong>تاريخ الإتمام:</strong>
                                                <?php echo date('d/m/Y', strtotime($cert['completion_date'])); ?>
                                            </div>
                                            
                                            <div class="detail-item mb-2">
                                                <i class="fas fa-clock text-primary me-2"></i>
                                                <strong>مدة الدورة:</strong>
                                                <?php echo $cert['duration_hours']; ?> ساعة
                                            </div>
                                            
                                            <?php if ($cert['instructor_name']): ?>
                                                <div class="detail-item mb-2">
                                                    <i class="fas fa-user-tie text-primary me-2"></i>
                                                    <strong>المدرب:</strong>
                                                    <?php echo htmlspecialchars($cert['instructor_name']); ?>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($cert['avg_score']): ?>
                                                <div class="detail-item mb-3">
                                                    <i class="fas fa-star text-primary me-2"></i>
                                                    <strong>المعدل:</strong>
                                                    <?php echo number_format($cert['avg_score'], 1); ?>%
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="certificate-id text-center mb-3">
                                            <small class="text-muted">رقم الشهادة</small>
                                            <div class="fw-bold text-primary">
                                                CERT-<?php echo str_pad($cert['id'], 6, '0', STR_PAD_LEFT); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent">
                                        <div class="d-grid gap-2">
                                            <a href="?generate=1&enrollment_id=<?php echo $cert['id']; ?>" 
                                               class="btn btn-primary" target="_blank">
                                                <i class="fas fa-download me-2"></i>
                                                تحميل الشهادة
                                            </a>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                    onclick="previewCertificate(<?php echo $cert['id']; ?>)">
                                                <i class="fas fa-eye me-2"></i>
                                                معاينة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Statistics -->
                    <div class="row mt-5">
                        <div class="col">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                                        إحصائيات الإنجاز
                                    </h5>
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <div class="stat-item">
                                                <h3 class="fw-bold text-primary"><?php echo count($certificates); ?></h3>
                                                <p class="mb-0">شهادة مكتملة</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="stat-item">
                                                <h3 class="fw-bold text-success">
                                                    <?php echo array_sum(array_column($certificates, 'duration_hours')); ?>
                                                </h3>
                                                <p class="mb-0">ساعة تدريب</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="stat-item">
                                                <h3 class="fw-bold text-info">
                                                    <?php 
                                                    $scores = array_filter(array_column($certificates, 'avg_score'));
                                                    echo !empty($scores) ? number_format(array_sum($scores) / count($scores), 1) : '0';
                                                    ?>%
                                                </h3>
                                                <p class="mb-0">المعدل العام</p>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="stat-item">
                                                <h3 class="fw-bold text-warning">
                                                    <?php echo date('Y'); ?>
                                                </h3>
                                                <p class="mb-0">السنة الحالية</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
</div>

<!-- Certificate Preview Modal -->
<div class="modal fade" id="certificatePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة الشهادة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="certificatePreview" class="certificate-preview">
                    <!-- Certificate preview will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="downloadCertBtn">
                    <i class="fas fa-download me-2"></i>تحميل
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.certificate-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.certificate-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.certificate-details .detail-item {
    font-size: 0.9rem;
}

.certificate-id {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
}

.stat-item {
    padding: 1rem;
}

.certificate-preview {
    background: white;
    border: 2px solid #ddd;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    min-height: 400px;
    position: relative;
    background-image: 
        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
}

.certificate-preview::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 3px solid #667eea;
    border-radius: 8px;
}

.certificate-preview h2 {
    color: #667eea;
    font-size: 2.5rem;
    margin-bottom: 30px;
    font-weight: 700;
}

.certificate-preview h3 {
    color: #333;
    font-size: 1.8rem;
    margin: 20px 0;
}

.certificate-preview .course-title {
    color: #764ba2;
    font-size: 2rem;
    font-weight: 600;
    margin: 30px 0;
}
</style>

<script>
function previewCertificate(enrollmentId) {
    // Find the certificate data
    const certificates = <?php echo json_encode($certificates); ?>;
    const cert = certificates.find(c => c.id == enrollmentId);
    
    if (cert) {
        const preview = `
            <div class="certificate-preview">
                <h2><i class="fas fa-certificate me-3"></i>شهادة إتمام</h2>
                <p style="font-size: 1.2rem; margin: 20px 0;">نشهد بأن</p>
                <h3 style="color: #667eea; font-size: 2.2rem;"><?php echo htmlspecialchars($_SESSION['user_name']); ?></h3>
                <p style="font-size: 1.2rem; margin: 20px 0;">قد أتم بنجاح دورة</p>
                <div class="course-title">${cert.course_title}</div>
                <p style="font-size: 1.1rem; margin: 30px 0;">
                    بمدة ${cert.duration_hours} ساعة تدريبية
                    ${cert.avg_score ? `بمعدل ${parseFloat(cert.avg_score).toFixed(1)}%` : ''}
                </p>
                <div style="margin-top: 40px;">
                    <p style="font-size: 1rem; color: #666;">تاريخ الإتمام: ${new Date(cert.completion_date).toLocaleDateString('ar-SA')}</p>
                    <p style="font-size: 0.9rem; color: #999;">رقم الشهادة: CERT-${cert.id.toString().padStart(6, '0')}</p>
                </div>
                ${cert.instructor_name ? `<p style="margin-top: 30px; font-size: 1rem;">المدرب: ${cert.instructor_name}</p>` : ''}
                <div style="margin-top: 40px;">
                    <p style="font-size: 0.9rem; color: #667eea; font-weight: 600;">منصة التدريب المهني</p>
                </div>
            </div>
        `;
        
        document.getElementById('certificatePreview').innerHTML = preview;
        document.getElementById('downloadCertBtn').onclick = function() {
            window.open(`?generate=1&enrollment_id=${enrollmentId}`, '_blank');
        };
        
        new bootstrap.Modal(document.getElementById('certificatePreviewModal')).show();
    }
}
</script>

<?php
// Certificate generation function
function generateCertificate($enrollment, $student_name) {
    // In a real application, you would use a PDF library like TCPDF or FPDF
    // For now, we'll create a simple HTML certificate
    
    header('Content-Type: text/html; charset=utf-8');
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>شهادة إتمام - <?php echo htmlspecialchars($enrollment['course_title']); ?></title>
        <style>
            body {
                font-family: 'Arial', sans-serif;
                margin: 0;
                padding: 40px;
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            }
            .certificate {
                background: white;
                border: 10px solid #667eea;
                border-radius: 20px;
                padding: 60px;
                text-align: center;
                max-width: 800px;
                margin: 0 auto;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                position: relative;
            }
            .certificate::before {
                content: '';
                position: absolute;
                top: 30px;
                left: 30px;
                right: 30px;
                bottom: 30px;
                border: 3px solid #764ba2;
                border-radius: 10px;
            }
            .header {
                color: #667eea;
                font-size: 3rem;
                margin-bottom: 40px;
                font-weight: bold;
            }
            .student-name {
                color: #333;
                font-size: 2.5rem;
                margin: 30px 0;
                font-weight: bold;
                border-bottom: 2px solid #667eea;
                padding-bottom: 10px;
                display: inline-block;
            }
            .course-title {
                color: #764ba2;
                font-size: 2.2rem;
                margin: 40px 0;
                font-weight: 600;
            }
            .details {
                font-size: 1.3rem;
                margin: 30px 0;
                color: #555;
            }
            .footer {
                margin-top: 60px;
                font-size: 1rem;
                color: #666;
            }
            .cert-number {
                position: absolute;
                bottom: 20px;
                right: 30px;
                font-size: 0.9rem;
                color: #999;
            }
            @media print {
                body { background: white; padding: 0; }
                .certificate { border: 5px solid #667eea; box-shadow: none; }
            }
        </style>
    </head>
    <body>
        <div class="certificate">
            <div class="header">
                🏆 شهادة إتمام 🏆
            </div>
            
            <p style="font-size: 1.5rem; margin: 30px 0;">نشهد بأن</p>
            
            <div class="student-name">
                <?php echo htmlspecialchars($student_name); ?>
            </div>
            
            <p style="font-size: 1.5rem; margin: 30px 0;">قد أتم بنجاح دورة</p>
            
            <div class="course-title">
                <?php echo htmlspecialchars($enrollment['course_title']); ?>
            </div>
            
            <div class="details">
                بمدة <?php echo $enrollment['duration_hours']; ?> ساعة تدريبية<br>
                <?php if ($enrollment['instructor_name']): ?>
                    تحت إشراف: <?php echo htmlspecialchars($enrollment['instructor_name']); ?>
                <?php endif; ?>
            </div>
            
            <div class="footer">
                <p>تاريخ الإتمام: <?php echo date('d/m/Y', strtotime($enrollment['completion_date'])); ?></p>
                <p style="color: #667eea; font-weight: bold; font-size: 1.2rem;">منصة التدريب المهني</p>
                <p style="font-size: 0.9rem;">هذه الشهادة معتمدة ومعترف بها</p>
            </div>
            
            <div class="cert-number">
                رقم الشهادة: CERT-<?php echo str_pad($enrollment['id'], 6, '0', STR_PAD_LEFT); ?>
            </div>
        </div>
        
        <script>
            window.onload = function() {
                window.print();
            }
        </script>
    </body>
    </html>
    <?php
}

include '../includes/footer.php';
?>
