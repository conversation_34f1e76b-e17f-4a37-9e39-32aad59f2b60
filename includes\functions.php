<?php
require_once dirname(__DIR__) . '/config/database.php';

function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function redirect($url) {
    header("Location: $url");
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function hasRole($required_role) {
    if (!isLoggedIn()) {
        return false;
    }
    return $_SESSION['user_role'] === $required_role;
}

function requireRole($required_role) {
    if (!hasRole($required_role)) {
        redirect('/course/auth/login.php');
    }
}

function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

function generateToken() {
    return bin2hex(random_bytes(32));
}

function uploadFile($file, $upload_dir = 'uploads/') {
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $file_name = uniqid() . '.' . $file_extension;
    $file_path = $upload_dir . $file_name;
    
    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        return $file_path;
    }
    return false;
}

function formatDate($date, $format = 'Y-m-d') {
    return date($format, strtotime($date));
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'منذ لحظات';
    } elseif ($time < 3600) {
        return 'منذ ' . floor($time/60) . ' دقيقة';
    } elseif ($time < 86400) {
        return 'منذ ' . floor($time/3600) . ' ساعة';
    } elseif ($time < 2592000) {
        return 'منذ ' . floor($time/86400) . ' يوم';
    } else {
        return 'منذ ' . floor($time/2592000) . ' شهر';
    }
}

function getTotalStudents() {
    global $conn;
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE role = 'student'");
    $stmt->execute();
    return $stmt->fetchColumn();
}

function getTotalInstructors() {
    global $conn;
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE role = 'instructor'");
    $stmt->execute();
    return $stmt->fetchColumn();
}

function getTotalCourses() {
    global $conn;
    $stmt = $conn->prepare("SELECT COUNT(*) FROM courses WHERE status = 'active'");
    $stmt->execute();
    return $stmt->fetchColumn();
}

function getActiveStudents() {
    global $conn;
    $stmt = $conn->prepare("SELECT COUNT(DISTINCT user_id) FROM enrollments WHERE status = 'active'");
    $stmt->execute();
    return $stmt->fetchColumn();
}

function showAlert($message, $type = 'info') {
    return "<div class='alert alert-$type alert-dismissible fade show' role='alert'>
                $message
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}

function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function generateSlug($string) {
    $string = transliterate($string);
    $string = strtolower($string);
    $string = preg_replace('/[^a-z0-9-]/', '-', $string);
    $string = preg_replace('/-+/', '-', $string);
    return trim($string, '-');
}

function transliterate($string) {
    $arabic = ['ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'];
    $english = ['a', 'b', 't', 'th', 'j', 'h', 'kh', 'd', 'th', 'r', 'z', 's', 'sh', 's', 'd', 't', 'th', 'a', 'gh', 'f', 'q', 'k', 'l', 'm', 'n', 'h', 'w', 'y'];
    return str_replace($arabic, $english, $string);
}

function checkPermission($user_id, $permission) {
    global $conn;
    $stmt = $conn->prepare("
        SELECT COUNT(*) FROM user_permissions up 
        JOIN permissions p ON up.permission_id = p.id 
        WHERE up.user_id = ? AND p.name = ?
    ");
    $stmt->execute([$user_id, $permission]);
    return $stmt->fetchColumn() > 0;
}

// Additional helper functions for enhanced functionality

// Format file size
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' جيجابايت';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' ميجابايت';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' كيلوبايت';
    } else {
        return $bytes . ' بايت';
    }
}



// Send notification
function sendNotification($user_id, $title, $message, $type = 'info', $action_url = null) {
    global $conn;
    try {
        $stmt = $conn->prepare("
            INSERT INTO notifications (user_id, title, message, type, action_url)
            VALUES (?, ?, ?, ?, ?)
        ");
        return $stmt->execute([$user_id, $title, $message, $type, $action_url]);
    } catch (PDOException $e) {
        return false;
    }
}

// Log activity
function logActivity($user_id, $action, $description, $ip_address = null) {
    global $conn;
    if (!$ip_address) {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    try {
        $stmt = $conn->prepare("
            INSERT INTO activity_logs (user_id, action, description, ip_address)
            VALUES (?, ?, ?, ?)
        ");
        return $stmt->execute([$user_id, $action, $description, $ip_address]);
    } catch (PDOException $e) {
        return false;
    }
}

// Get unread notifications count
function getUnreadNotificationsCount($user_id) {
    global $conn;
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
        $stmt->execute([$user_id]);
        return $stmt->fetchColumn();
    } catch (PDOException $e) {
        return 0;
    }
}

// Check if user can access course
function canAccessCourse($user_id, $course_id) {
    global $conn;

    // Check if user is enrolled
    $stmt = $conn->prepare("
        SELECT status FROM enrollments
        WHERE user_id = ? AND course_id = ? AND status IN ('active', 'completed')
    ");
    $stmt->execute([$user_id, $course_id]);
    $enrollment = $stmt->fetch();

    if ($enrollment) {
        return true;
    }

    // Check if user is instructor of the course
    $stmt = $conn->prepare("SELECT instructor_id FROM courses WHERE id = ?");
    $stmt->execute([$course_id]);
    $course = $stmt->fetch();

    if ($course && $course['instructor_id'] == $user_id) {
        return true;
    }

    // Check if user is admin
    if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin') {
        return true;
    }

    return false;
}



// Get setting value
function getSetting($key, $default = null) {
    global $conn;

    try {
        $stmt = $conn->prepare("SELECT value FROM settings WHERE key_name = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();

        return $result ? $result['value'] : $default;
    } catch (PDOException $e) {
        return $default;
    }
}

// Update setting value
function updateSetting($key, $value) {
    global $conn;

    try {
        $stmt = $conn->prepare("
            INSERT INTO settings (key_name, value) VALUES (?, ?)
            ON DUPLICATE KEY UPDATE value = VALUES(value)
        ");
        return $stmt->execute([$key, $value]);
    } catch (PDOException $e) {
        return false;
    }
}
?>