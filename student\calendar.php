<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('student');

$user_id = $_SESSION['user_id'];
$current_date = date('Y-m-d');
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
$month = isset($_GET['month']) ? (int)$_GET['month'] : date('n');

// Validate month and year
if ($month < 1 || $month > 12) $month = date('n');
if ($year < 2020 || $year > 2030) $year = date('Y');

// Get month boundaries
$start_date = "$year-" . str_pad($month, 2, '0', STR_PAD_LEFT) . "-01";
$end_date = date('Y-m-t', strtotime($start_date));

// Get student's enrolled courses and available courses
$my_courses_stmt = $conn->prepare("
    SELECT DISTINCT c.id, c.title, c.start_date, c.end_date, c.status
    FROM enrollments e
    JOIN courses c ON e.course_id = c.id
    WHERE e.user_id = ? AND e.status IN ('active', 'completed')
");
$my_courses_stmt->execute([$user_id]);
$my_courses = $my_courses_stmt->fetchAll(PDO::FETCH_ASSOC);
$my_course_ids = array_column($my_courses, 'id');

// Get sessions for enrolled courses
$my_sessions_stmt = $conn->prepare("
    SELECT cs.*, c.title as course_title, c.id as course_id,
           a.status as attendance_status
    FROM course_sessions cs
    JOIN courses c ON cs.course_id = c.id
    JOIN enrollments e ON c.id = e.course_id
    LEFT JOIN attendance a ON cs.id = a.session_id AND a.user_id = ?
    WHERE e.user_id = ? AND e.status IN ('active', 'completed')
    AND cs.session_date BETWEEN ? AND ?
    ORDER BY cs.session_date, cs.start_time
");
$my_sessions_stmt->execute([$user_id, $user_id, $start_date, $end_date]);
$my_sessions = $my_sessions_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get available courses (not enrolled)
$available_courses_stmt = $conn->prepare("
    SELECT c.*, cat.name as category_name, u.name as instructor_name
    FROM courses c
    LEFT JOIN categories cat ON c.category_id = cat.id
    LEFT JOIN users u ON c.instructor_id = u.id
    WHERE c.status = 'active' 
    AND c.start_date BETWEEN ? AND ?
    AND c.id NOT IN (" . (empty($my_course_ids) ? '0' : implode(',', $my_course_ids)) . ")
    ORDER BY c.start_date
");
$available_courses_stmt->execute([$start_date, $end_date]);
$available_courses = $available_courses_stmt->fetchAll(PDO::FETCH_ASSOC);

// Group events by date
$events_by_date = [];

// Add my sessions
foreach ($my_sessions as $session) {
    $date = $session['session_date'];
    if (!isset($events_by_date[$date])) {
        $events_by_date[$date] = [];
    }
    $events_by_date[$date][] = [
        'type' => 'my_session',
        'data' => $session
    ];
}

// Add available courses
foreach ($available_courses as $course) {
    $date = $course['start_date'];
    if (!isset($events_by_date[$date])) {
        $events_by_date[$date] = [];
    }
    $events_by_date[$date][] = [
        'type' => 'available_course',
        'data' => $course
    ];
}

// Add my courses start dates
foreach ($my_courses as $course) {
    if ($course['start_date'] >= $start_date && $course['start_date'] <= $end_date) {
        $date = $course['start_date'];
        if (!isset($events_by_date[$date])) {
            $events_by_date[$date] = [];
        }
        $events_by_date[$date][] = [
            'type' => 'my_course_start',
            'data' => $course
        ];
    }
}

$page_title = 'التقويم الدراسي';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <?php include '../includes/sidebar.php'; ?>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">التقويم الدراسي</h1>
                    <a href="browse-courses.php" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>تصفح الدورات
                    </a>
                </div>

                <!-- Calendar Navigation -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h4 class="mb-0"><?php echo getMonthName($month) . ' ' . $year; ?></h4>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group" role="group">
                                    <a href="?year=<?php echo $month == 1 ? $year - 1 : $year; ?>&month=<?php echo $month == 1 ? 12 : $month - 1; ?>" 
                                       class="btn btn-outline-primary">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                    <a href="?year=<?php echo date('Y'); ?>&month=<?php echo date('n'); ?>" 
                                       class="btn btn-outline-primary">اليوم</a>
                                    <a href="?year=<?php echo $month == 12 ? $year + 1 : $year; ?>&month=<?php echo $month == 12 ? 1 : $month + 1; ?>" 
                                       class="btn btn-outline-primary">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Legend -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <span class="badge bg-primary me-2"></span>
                                <small>جلساتي</small>
                            </div>
                            <div class="col-md-3">
                                <span class="badge bg-success me-2"></span>
                                <small>دوراتي</small>
                            </div>
                            <div class="col-md-3">
                                <span class="badge bg-warning me-2"></span>
                                <small>دورات متاحة</small>
                            </div>
                            <div class="col-md-3">
                                <span class="badge bg-info me-2"></span>
                                <small>حضرت</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Calendar -->
                <div class="card">
                    <div class="card-body p-0">
                        <div class="calendar">
                            <!-- Calendar Header -->
                            <div class="row g-0 calendar-header">
                                <div class="col text-center py-2 text-white fw-bold">الأحد</div>
                                <div class="col text-center py-2 text-white fw-bold">الاثنين</div>
                                <div class="col text-center py-2 text-white fw-bold">الثلاثاء</div>
                                <div class="col text-center py-2 text-white fw-bold">الأربعاء</div>
                                <div class="col text-center py-2 text-white fw-bold">الخميس</div>
                                <div class="col text-center py-2 text-white fw-bold">الجمعة</div>
                                <div class="col text-center py-2 text-white fw-bold">السبت</div>
                            </div>

                            <!-- Calendar Body -->
                            <?php
                            $first_day = date('w', strtotime($start_date));
                            $days_in_month = date('t', strtotime($start_date));
                            $day = 1;
                            
                            for ($week = 0; $week < 6; $week++):
                                if ($day > $days_in_month) break;
                            ?>
                                <div class="row g-0">
                                    <?php for ($dow = 0; $dow < 7; $dow++): ?>
                                        <div class="col calendar-day border-end border-bottom" style="min-height: 120px;">
                                            <?php
                                            if (($week == 0 && $dow < $first_day) || $day > $days_in_month) {
                                                echo '<div class="p-2"></div>';
                                            } else {
                                                $current_day_date = sprintf('%04d-%02d-%02d', $year, $month, $day);
                                                $is_today = $current_day_date == $current_date;
                                                $has_events = isset($events_by_date[$current_day_date]);
                                            ?>
                                                <div class="p-2 h-100" onclick="showDayEvents('<?php echo $current_day_date; ?>')" style="cursor: pointer;">
                                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                                        <span class="fw-bold <?php echo $is_today ? 'text-primary' : ''; ?>">
                                                            <?php echo $day; ?>
                                                        </span>
                                                        <?php if ($is_today): ?>
                                                            <span class="badge bg-primary">اليوم</span>
                                                        <?php endif; ?>
                                                    </div>
                                                    
                                                    <?php if ($has_events): ?>
                                                        <?php foreach ($events_by_date[$current_day_date] as $index => $event): ?>
                                                            <?php if ($index < 2): ?>
                                                                <div class="mb-1">
                                                                    <?php if ($event['type'] === 'my_session'): ?>
                                                                        <div class="small <?php echo $event['data']['attendance_status'] === 'present' ? 'bg-info' : 'bg-primary'; ?> text-white px-2 py-1 rounded" 
                                                                             style="font-size: 0.75rem;">
                                                                            <div class="fw-bold"><?php echo date('H:i', strtotime($event['data']['start_time'])); ?></div>
                                                                            <div><?php echo htmlspecialchars(substr($event['data']['title'], 0, 15)); ?></div>
                                                                        </div>
                                                                    <?php elseif ($event['type'] === 'my_course_start'): ?>
                                                                        <div class="small bg-success text-white px-2 py-1 rounded" 
                                                                             style="font-size: 0.75rem;">
                                                                            <div class="fw-bold"><i class="fas fa-star"></i></div>
                                                                            <div><?php echo htmlspecialchars(substr($event['data']['title'], 0, 15)); ?></div>
                                                                        </div>
                                                                    <?php elseif ($event['type'] === 'available_course'): ?>
                                                                        <div class="small bg-warning text-dark px-2 py-1 rounded" 
                                                                             style="font-size: 0.75rem; cursor: pointer;"
                                                                             onclick="event.stopPropagation(); showCourseDetails(<?php echo $event['data']['id']; ?>)">
                                                                            <div class="fw-bold"><i class="fas fa-plus"></i></div>
                                                                            <div><?php echo htmlspecialchars(substr($event['data']['title'], 0, 15)); ?></div>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                            <?php endif; ?>
                                                        <?php endforeach; ?>
                                                        <?php if (count($events_by_date[$current_day_date]) > 2): ?>
                                                            <div class="small text-muted">
                                                                +<?php echo count($events_by_date[$current_day_date]) - 2; ?> المزيد...
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>
                                            <?php
                                                $day++;
                                            }
                                            ?>
                                        </div>
                                    <?php endfor; ?>
                                </div>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Day Events Modal -->
<div class="modal fade" id="dayEventsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">أحداث اليوم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="dayEventsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Course Details Modal -->
<div class="modal fade" id="courseDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الدورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="courseDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
.calendar {
    font-size: 0.9rem;
}

.calendar-day {
    transition: background-color 0.2s;
}

.calendar-day:hover {
    background-color: #f8f9fa;
}

.calendar-header .col {
    background-color: #0d6efd;
}

@media (max-width: 768px) {
    .calendar {
        font-size: 0.8rem;
    }
    
    .calendar-day {
        min-height: 80px !important;
    }
}
</style>

<script>
function showDayEvents(date) {
    document.getElementById('dayEventsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';
    
    const modal = new bootstrap.Modal(document.getElementById('dayEventsModal'));
    modal.show();
    
    fetch(`calendar-day-events.php?date=${date}`)
        .then(response => response.text())
        .then(data => {
            document.getElementById('dayEventsContent').innerHTML = data;
        })
        .catch(error => {
            document.getElementById('dayEventsContent').innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>';
        });
}

function showCourseDetails(courseId) {
    document.getElementById('courseDetailsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';
    
    const modal = new bootstrap.Modal(document.getElementById('courseDetailsModal'));
    modal.show();
    
    fetch(`course-details.php?id=${courseId}`)
        .then(response => response.text())
        .then(data => {
            document.getElementById('courseDetailsContent').innerHTML = data;
        })
        .catch(error => {
            document.getElementById('courseDetailsContent').innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>';
        });
}
</script>

<?php include '../includes/footer.php'; ?>
