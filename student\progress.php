<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('student');

$user_id = $_SESSION['user_id'];

// Get student's enrolled courses with progress
$courses_stmt = $conn->prepare("
    SELECT e.*, c.title, c.description, c.duration_hours, c.instructor_id,
           u.name as instructor_name,
           (SELECT COUNT(*) FROM course_sessions cs WHERE cs.course_id = c.id) as total_sessions,
           (SELECT COUNT(*) FROM attendance a 
            JOIN course_sessions cs ON a.session_id = cs.id 
            WHERE cs.course_id = c.id AND a.user_id = e.user_id AND a.status = 'present') as attended_sessions,
           (SELECT AVG(score) FROM evaluations ev WHERE ev.user_id = e.user_id AND ev.course_id = c.id AND ev.score IS NOT NULL) as avg_score,
           (SELECT COUNT(*) FROM evaluations ev WHERE ev.user_id = e.user_id AND ev.course_id = c.id) as total_evaluations,
           (SELECT COUNT(*) FROM evaluations ev WHERE ev.user_id = e.user_id AND ev.course_id = c.id AND ev.score IS NOT NULL) as graded_evaluations
    FROM enrollments e
    JOIN courses c ON e.course_id = c.id
    LEFT JOIN users u ON c.instructor_id = u.id
    WHERE e.user_id = ?
    ORDER BY e.created_at DESC
");
$courses_stmt->execute([$user_id]);
$courses = $courses_stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate overall statistics
$total_courses = count($courses);
$completed_courses = count(array_filter($courses, function($c) { return $c['status'] === 'completed'; }));
$active_courses = count(array_filter($courses, function($c) { return $c['status'] === 'active'; }));
$total_hours = array_sum(array_column($courses, 'duration_hours'));

// Get recent evaluations
$recent_evaluations_stmt = $conn->prepare("
    SELECT e.*, c.title as course_title
    FROM evaluations e
    JOIN courses c ON e.course_id = c.id
    WHERE e.user_id = ? AND e.score IS NOT NULL
    ORDER BY e.graded_at DESC
    LIMIT 5
");
$recent_evaluations_stmt->execute([$user_id]);
$recent_evaluations = $recent_evaluations_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'تقدمي الدراسي';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                دوراتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="schedule.php">
                                <i class="fas fa-calendar-alt"></i>
                                جدولي الدراسي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="progress.php">
                                <i class="fas fa-chart-line"></i>
                                تقدمي الدراسي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i>
                                شهاداتي
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">تقدمي الدراسي</h1>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="exportProgress()">
                            <i class="fas fa-download me-2"></i>تصدير التقرير
                        </button>
                    </div>
                </div>

                <!-- Overall Statistics -->
                <div class="row g-4 mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الدورات</h6>
                                        <h2 class="fw-bold"><?php echo $total_courses; ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-graduation-cap fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">دورات مكتملة</h6>
                                        <h2 class="fw-bold"><?php echo $completed_courses; ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">دورات نشطة</h6>
                                        <h2 class="fw-bold"><?php echo $active_courses; ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-play-circle fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">ساعات التدريب</h6>
                                        <h2 class="fw-bold"><?php echo $total_hours; ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course Progress -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            تقدم الدورات
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($courses)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                                <h5>لا توجد دورات مسجلة</h5>
                                <p class="text-muted">ابدأ بالتسجيل في دورة لمتابعة تقدمك</p>
                                <a href="browse-courses.php" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>تصفح الدورات
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="row g-4">
                                <?php foreach ($courses as $course): ?>
                                    <?php
                                    $attendance_percentage = $course['total_sessions'] > 0 ? 
                                        round(($course['attended_sessions'] / $course['total_sessions']) * 100, 1) : 0;
                                    $evaluation_percentage = $course['total_evaluations'] > 0 ? 
                                        round(($course['graded_evaluations'] / $course['total_evaluations']) * 100, 1) : 0;
                                    $overall_progress = round(($attendance_percentage + $evaluation_percentage) / 2, 1);
                                    ?>
                                    <div class="col-lg-6">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-3">
                                                    <h6 class="card-title fw-bold mb-0"><?php echo htmlspecialchars($course['title']); ?></h6>
                                                    <span class="badge bg-<?php 
                                                        echo match($course['status']) {
                                                            'active' => 'primary',
                                                            'completed' => 'success',
                                                            'pending' => 'warning',
                                                            default => 'secondary'
                                                        };
                                                    ?>">
                                                        <?php 
                                                        echo match($course['status']) {
                                                            'active' => 'نشط',
                                                            'completed' => 'مكتمل',
                                                            'pending' => 'معلق',
                                                            default => $course['status']
                                                        };
                                                        ?>
                                                    </span>
                                                </div>
                                                
                                                <div class="progress-section mb-3">
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span class="small fw-bold">التقدم الإجمالي</span>
                                                        <span class="small fw-bold"><?php echo $overall_progress; ?>%</span>
                                                    </div>
                                                    <div class="progress mb-3" style="height: 8px;">
                                                        <div class="progress-bar" style="width: <?php echo $overall_progress; ?>%"></div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row text-center">
                                                    <div class="col-4">
                                                        <div class="border-end">
                                                            <h6 class="fw-bold text-primary"><?php echo $course['attended_sessions']; ?>/<?php echo $course['total_sessions']; ?></h6>
                                                            <small class="text-muted">الحضور</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="border-end">
                                                            <h6 class="fw-bold text-success">
                                                                <?php echo $course['avg_score'] ? number_format($course['avg_score'], 1) : '0'; ?>%
                                                            </h6>
                                                            <small class="text-muted">المعدل</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <h6 class="fw-bold text-info"><?php echo $course['graded_evaluations']; ?>/<?php echo $course['total_evaluations']; ?></h6>
                                                        <small class="text-muted">التقييمات</small>
                                                    </div>
                                                </div>
                                                
                                                <?php if ($course['instructor_name']): ?>
                                                    <div class="mt-3 pt-3 border-top">
                                                        <small class="text-muted">
                                                            <i class="fas fa-user-tie me-1"></i>
                                                            المدرب: <?php echo htmlspecialchars($course['instructor_name']); ?>
                                                        </small>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Evaluations -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-clipboard-check me-2"></i>
                                    آخر التقييمات
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_evaluations)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                                        <h6>لا توجد تقييمات بعد</h6>
                                        <p class="text-muted">ستظهر تقييماتك هنا بعد إكمال الاختبارات والواجبات</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>التقييم</th>
                                                    <th>الدورة</th>
                                                    <th>الدرجة</th>
                                                    <th>التقدير</th>
                                                    <th>التاريخ</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recent_evaluations as $eval): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($eval['title']); ?></strong>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($eval['evaluation_type']); ?></small>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($eval['course_title']); ?></td>
                                                        <td>
                                                            <strong><?php echo $eval['score']; ?></strong> / <?php echo $eval['max_score']; ?>
                                                            <br><small class="text-muted"><?php echo round(($eval['score'] / $eval['max_score']) * 100, 1); ?>%</small>
                                                        </td>
                                                        <td>
                                                            <?php if ($eval['grade']): ?>
                                                                <span class="badge bg-<?php 
                                                                    echo match(substr($eval['grade'], 0, 1)) {
                                                                        'A' => 'success',
                                                                        'B' => 'primary',
                                                                        'C' => 'warning',
                                                                        'D' => 'info',
                                                                        default => 'danger'
                                                                    };
                                                                ?>"><?php echo $eval['grade']; ?></span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td><?php echo date('d/m/Y', strtotime($eval['graded_at'])); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    إحصائيات سريعة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span class="small">معدل الحضور</span>
                                        <span class="small fw-bold">
                                            <?php 
                                            $total_sessions_all = array_sum(array_column($courses, 'total_sessions'));
                                            $total_attended_all = array_sum(array_column($courses, 'attended_sessions'));
                                            $overall_attendance = $total_sessions_all > 0 ? round(($total_attended_all / $total_sessions_all) * 100, 1) : 0;
                                            echo $overall_attendance; 
                                            ?>%
                                        </span>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-info" style="width: <?php echo $overall_attendance; ?>%"></div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span class="small">معدل الإكمال</span>
                                        <span class="small fw-bold">
                                            <?php 
                                            $completion_rate = $total_courses > 0 ? round(($completed_courses / $total_courses) * 100, 1) : 0;
                                            echo $completion_rate; 
                                            ?>%
                                        </span>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-success" style="width: <?php echo $completion_rate; ?>%"></div>
                                    </div>
                                </div>
                                
                                <div class="text-center mt-4">
                                    <canvas id="progressChart" width="200" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
// Progress Chart
const ctx = document.getElementById('progressChart').getContext('2d');
new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['مكتملة', 'نشطة', 'معلقة'],
        datasets: [{
            data: [<?php echo $completed_courses; ?>, <?php echo $active_courses; ?>, <?php echo $total_courses - $completed_courses - $active_courses; ?>],
            backgroundColor: ['#28a745', '#007bff', '#ffc107']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function exportProgress() {
    // Create progress report
    let report = 'تقرير التقدم الدراسي\n';
    report += '===================\n\n';
    report += `إجمالي الدورات: ${<?php echo $total_courses; ?>}\n`;
    report += `الدورات المكتملة: ${<?php echo $completed_courses; ?>}\n`;
    report += `الدورات النشطة: ${<?php echo $active_courses; ?>}\n`;
    report += `إجمالي ساعات التدريب: ${<?php echo $total_hours; ?>}\n\n`;
    
    report += 'تفاصيل الدورات:\n';
    report += '================\n';
    
    <?php foreach ($courses as $course): ?>
        report += '<?php echo addslashes($course["title"]); ?>\n';
        report += 'الحالة: <?php echo $course["status"]; ?>\n';
        report += 'الحضور: <?php echo $course["attended_sessions"]; ?>/<?php echo $course["total_sessions"]; ?>\n';
        report += 'المعدل: <?php echo $course["avg_score"] ? number_format($course["avg_score"], 1) : "0"; ?>%\n\n';
    <?php endforeach; ?>
    
    const blob = new Blob([report], { type: 'text/plain;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'تقرير_التقدم_الدراسي.txt');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>

<?php include '../includes/footer.php'; ?>
