# منصة التدريب المهني - Training Platform

منصة تدريب متكاملة متخصصة في علوم الحاسوب وتقنية المعلومات، مصممة لتوفير تجربة تعليمية شاملة للطلاب والمدربين والمديرين.

## 🌟 الميزات الرئيسية

### 👥 إدارة المستخدمين
- نظام تسجيل دخول آمن مع أدوار متعددة (مدير، مدرب، طالب)
- إدارة الملفات الشخصية والصور الرمزية
- نظام استعادة كلمة المرور عبر البريد الإلكتروني
- تتبع نشاط المستخدمين وسجل الأنشطة

### 📚 إدارة الدورات
- إنشاء وتحرير الدورات التدريبية
- تصنيف الدورات حسب المجالات
- رفع وإدارة المواد التعليمية (PDF, فيديو, صور, مستندات)
- جدولة الجلسات التدريبية
- نظام التسجيل والموافقة على الدورات

### 📊 نظام التقييم والشهادات
- إنشاء اختبارات وواجبات متنوعة
- تقييم الطلاب ووضع الدرجات
- إصدار شهادات إتمام معتمدة
- تتبع تقدم الطلاب ومعدلاتهم

### 🔔 نظام الإشعارات
- إشعارات فورية داخل المنصة
- إشعارات البريد الإلكتروني
- تنبيهات الجلسات والمواعيد المهمة
- إشعارات التقييمات والنتائج

### 📈 التقارير والإحصائيات
- تقارير شاملة عن الطلاب والدورات
- إحصائيات الحضور والأداء
- تحليل البيانات والمؤشرات
- تصدير التقارير بصيغ مختلفة

### 🎯 ميزات إضافية
- نظام الحضور والغياب
- تقييم الدورات والمدربين
- نظام الرسائل الداخلية
- تقويم الأحداث والجلسات
- نظام النسخ الاحتياطي

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 8.0+
- **Database**: MySQL 8.0+
- **Frontend**: HTML5, CSS3, JavaScript ES6+
- **Framework**: Bootstrap 5.3
- **Icons**: Font Awesome 6.0
- **Charts**: Chart.js
- **Fonts**: Google Fonts (Cairo)

## 📋 المتطلبات

- خادم ويب (Apache/Nginx)
- PHP 8.0 أو أحدث
- MySQL 8.0 أو أحدث
- ذاكرة 512MB على الأقل
- مساحة تخزين 1GB على الأقل

## 🚀 التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/training-platform.git
cd training-platform
```

### 2. إعداد قاعدة البيانات
1. أنشئ قاعدة بيانات جديدة في MySQL
2. استورد ملف `database/schema.sql`
3. (اختياري) استورد ملف `database/update.sql` للتحديثات

### 3. إعداد الإعدادات
1. انسخ ملف `config/database.php.example` إلى `config/database.php`
2. حدث إعدادات قاعدة البيانات في الملف

### 4. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 755 uploads/courses/
chmod 755 uploads/materials/
chmod 755 uploads/avatars/
```

### 5. الإعداد التلقائي
زر صفحة `setup.php` في المتصفح واتبع التعليمات

## 👤 بيانات الدخول الافتراضية

**المدير:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`

## 📁 هيكل المشروع

```
training-platform/
├── admin/              # لوحة تحكم المدير
├── instructor/         # لوحة تحكم المدرب
├── student/           # لوحة تحكم الطالب
├── auth/              # صفحات التوثيق
├── assets/            # الملفات الثابتة
│   ├── css/          # ملفات التصميم
│   ├── js/           # ملفات JavaScript
│   └── images/       # الصور
├── config/            # ملفات الإعداد
├── database/          # ملفات قاعدة البيانات
├── includes/          # الملفات المشتركة
├── uploads/           # ملفات المستخدمين
└── README.md
```

## 🔧 الإعدادات المتقدمة

### إعداد البريد الإلكتروني
1. ادخل إلى لوحة تحكم المدير
2. اذهب إلى الإعدادات
3. حدث إعدادات SMTP

### إعداد النسخ الاحتياطي
```bash
# إضافة مهمة cron للنسخ الاحتياطي اليومي
0 2 * * * /path/to/backup-script.sh
```

## 🔒 الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية من هجمات SQL Injection
- حماية من هجمات XSS
- تسجيل جميع الأنشطة المهمة
- جلسات آمنة مع انتهاء صلاحية

## 📱 التوافق

- متوافق مع جميع المتصفحات الحديثة
- تصميم متجاوب يعمل على الهواتف والأجهزة اللوحية
- دعم اللغة العربية بالكامل (RTL)

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشاكل، يرجى:
1. التحقق من سجلات الأخطاء
2. مراجعة متطلبات النظام
3. التأكد من صحة إعدادات قاعدة البيانات

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📞 التواصل

- البريد الإلكتروني: <EMAIL>
- الموقع: [www.trainingplatform.com](https://www.trainingplatform.com)

## 🔄 التحديثات

### الإصدار 1.0.0
- إطلاق النسخة الأولى من المنصة
- جميع الميزات الأساسية متوفرة
- دعم كامل للغة العربية

---

**ملاحظة**: هذا المشروع في مرحلة التطوير المستمر. نرحب بالاقتراحات والتحسينات.
