<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('instructor');

$instructor_id = $_SESSION['user_id'];
$student_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;

if (!$student_id) {
    header('Location: students.php');
    exit;
}

// Verify instructor has access to this student
$access_stmt = $conn->prepare("
    SELECT u.*, e.status as enrollment_status, e.created_at as enrollment_date,
           c.title as course_title, c.id as course_id
    FROM users u
    JOIN enrollments e ON u.id = e.user_id
    JOIN courses c ON e.course_id = c.id
    WHERE u.id = ? AND c.instructor_id = ? AND u.role = 'student'
    " . ($course_id > 0 ? "AND c.id = ?" : "") . "
    ORDER BY e.created_at DESC
");

$params = [$student_id, $instructor_id];
if ($course_id > 0) {
    $params[] = $course_id;
}

$access_stmt->execute($params);
$student_courses = $access_stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($student_courses)) {
    header('Location: students.php');
    exit;
}

$student = $student_courses[0]; // Get student basic info from first record

// Get student's attendance for instructor's courses
$attendance_stmt = $conn->prepare("
    SELECT cs.*, c.title as course_title, a.status as attendance_status,
           a.check_in_time, a.check_out_time, a.notes
    FROM course_sessions cs
    JOIN courses c ON cs.course_id = c.id
    LEFT JOIN attendance a ON cs.id = a.session_id AND a.user_id = ?
    WHERE c.instructor_id = ?
    " . ($course_id > 0 ? "AND c.id = ?" : "") . "
    ORDER BY cs.session_date DESC, cs.start_time DESC
    LIMIT 20
");

$attendance_params = [$student_id, $instructor_id];
if ($course_id > 0) {
    $attendance_params[] = $course_id;
}

$attendance_stmt->execute($attendance_params);
$attendance_records = $attendance_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get student's evaluations
$evaluations_stmt = $conn->prepare("
    SELECT ev.*, c.title as course_title
    FROM evaluations ev
    JOIN courses c ON ev.course_id = c.id
    WHERE ev.user_id = ? AND ev.instructor_id = ?
    " . ($course_id > 0 ? "AND c.id = ?" : "") . "
    ORDER BY ev.created_at DESC
");

$eval_params = [$student_id, $instructor_id];
if ($course_id > 0) {
    $eval_params[] = $course_id;
}

$evaluations_stmt->execute($eval_params);
$evaluations = $evaluations_stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate statistics
$total_sessions = count($attendance_records);
$attended_sessions = count(array_filter($attendance_records, function($r) { return $r['attendance_status'] === 'present'; }));
$attendance_rate = $total_sessions > 0 ? round(($attended_sessions / $total_sessions) * 100, 1) : 0;

$graded_evaluations = array_filter($evaluations, function($e) { return $e['score'] !== null; });
$avg_score = !empty($graded_evaluations) ? array_sum(array_column($graded_evaluations, 'score')) / count($graded_evaluations) : 0;

$page_title = 'ملف الطالب - ' . $student['name'];
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                دوراتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="sessions.php">
                                <i class="fas fa-calendar-alt"></i>
                                الجلسات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="students.php">
                                <i class="fas fa-user-graduate"></i>
                                الطلاب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="evaluations.php">
                                <i class="fas fa-clipboard-check"></i>
                                التقييمات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 fw-bold">ملف الطالب</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="students.php">الطلاب</a></li>
                                <li class="breadcrumb-item active"><?php echo htmlspecialchars($student['name']); ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="exportStudentReport()">
                            <i class="fas fa-download me-2"></i>تصدير التقرير
                        </button>
                        <a href="students.php" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>العودة
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- Student Info -->
                    <div class="col-lg-4 mb-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center p-4">
                                <div class="profile-image-container mb-3">
                                    <?php if ($student['profile_image'] && file_exists('../' . $student['profile_image'])): ?>
                                        <img src="../<?php echo htmlspecialchars($student['profile_image']); ?>" 
                                             alt="صورة الطالب" 
                                             class="rounded-circle" 
                                             style="width: 100px; height: 100px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                                             style="width: 100px; height: 100px;">
                                            <i class="fas fa-user-graduate fa-2x"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <h5 class="fw-bold mb-1"><?php echo htmlspecialchars($student['name']); ?></h5>
                                <p class="text-muted mb-3"><?php echo htmlspecialchars($student['email']); ?></p>
                                
                                <div class="row text-center mb-3">
                                    <div class="col-6">
                                        <h6 class="fw-bold text-primary"><?php echo $attendance_rate; ?>%</h6>
                                        <small class="text-muted">معدل الحضور</small>
                                    </div>
                                    <div class="col-6">
                                        <h6 class="fw-bold text-success"><?php echo number_format($avg_score, 1); ?>%</h6>
                                        <small class="text-muted">المعدل العام</small>
                                    </div>
                                </div>
                                
                                <?php if ($student['phone']): ?>
                                    <div class="mb-2">
                                        <i class="fas fa-phone me-2 text-muted"></i>
                                        <span><?php echo htmlspecialchars($student['phone']); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="mb-2">
                                    <i class="fas fa-calendar me-2 text-muted"></i>
                                    <span>انضم في <?php echo date('d/m/Y', strtotime($student['created_at'])); ?></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Enrolled Courses -->
                        <div class="card border-0 shadow-sm mt-4">
                            <div class="card-header">
                                <h6 class="mb-0">الدورات المسجلة</h6>
                            </div>
                            <div class="card-body p-0">
                                <div class="list-group list-group-flush">
                                    <?php foreach ($student_courses as $course): ?>
                                        <div class="list-group-item border-0">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1 fw-bold">
                                                        <a href="?id=<?php echo $student_id; ?>&course_id=<?php echo $course['course_id']; ?>" 
                                                           class="text-decoration-none">
                                                            <?php echo htmlspecialchars($course['course_title']); ?>
                                                        </a>
                                                    </h6>
                                                    <small class="text-muted">
                                                        تسجل في <?php echo date('d/m/Y', strtotime($course['enrollment_date'])); ?>
                                                    </small>
                                                </div>
                                                <span class="badge bg-<?php 
                                                    echo match($course['enrollment_status']) {
                                                        'active' => 'primary',
                                                        'completed' => 'success',
                                                        'pending' => 'warning',
                                                        default => 'secondary'
                                                    };
                                                ?>">
                                                    <?php 
                                                    echo match($course['enrollment_status']) {
                                                        'active' => 'نشط',
                                                        'completed' => 'مكتمل',
                                                        'pending' => 'معلق',
                                                        default => $course['enrollment_status']
                                                    };
                                                    ?>
                                                </span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Student Details -->
                    <div class="col-lg-8">
                        <!-- Attendance Records -->
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar-check me-2"></i>
                                    سجل الحضور
                                    <?php if ($course_id > 0): ?>
                                        - <?php echo htmlspecialchars($student['course_title']); ?>
                                    <?php endif; ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($attendance_records)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                        <h6>لا توجد سجلات حضور</h6>
                                        <p class="text-muted">لم يتم تسجيل أي حضور للطالب بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>التاريخ</th>
                                                    <th>الجلسة</th>
                                                    <th>الدورة</th>
                                                    <th>الحالة</th>
                                                    <th>وقت الدخول</th>
                                                    <th>وقت الخروج</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($attendance_records as $record): ?>
                                                    <tr>
                                                        <td><?php echo date('d/m/Y', strtotime($record['session_date'])); ?></td>
                                                        <td><?php echo htmlspecialchars($record['title']); ?></td>
                                                        <td><?php echo htmlspecialchars($record['course_title']); ?></td>
                                                        <td>
                                                            <?php if ($record['attendance_status']): ?>
                                                                <span class="badge bg-<?php 
                                                                    echo match($record['attendance_status']) {
                                                                        'present' => 'success',
                                                                        'late' => 'warning',
                                                                        'absent' => 'danger',
                                                                        'excused' => 'info',
                                                                        default => 'secondary'
                                                                    };
                                                                ?>">
                                                                    <?php 
                                                                    echo match($record['attendance_status']) {
                                                                        'present' => 'حاضر',
                                                                        'late' => 'متأخر',
                                                                        'absent' => 'غائب',
                                                                        'excused' => 'معذور',
                                                                        default => 'غير محدد'
                                                                    };
                                                                    ?>
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="badge bg-secondary">غير مسجل</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php echo $record['check_in_time'] ? date('H:i', strtotime($record['check_in_time'])) : '-'; ?>
                                                        </td>
                                                        <td>
                                                            <?php echo $record['check_out_time'] ? date('H:i', strtotime($record['check_out_time'])) : '-'; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Evaluations -->
                        <div class="card border-0 shadow-sm">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-clipboard-check me-2"></i>
                                    التقييمات والدرجات
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($evaluations)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                                        <h6>لا توجد تقييمات</h6>
                                        <p class="text-muted">لم يتم إنشاء أي تقييمات للطالب بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>التقييم</th>
                                                    <th>الدورة</th>
                                                    <th>النوع</th>
                                                    <th>الدرجة</th>
                                                    <th>النسبة</th>
                                                    <th>التقدير</th>
                                                    <th>التاريخ</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($evaluations as $eval): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($eval['title']); ?></strong>
                                                            <?php if ($eval['description']): ?>
                                                                <br><small class="text-muted"><?php echo htmlspecialchars($eval['description']); ?></small>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($eval['course_title']); ?></td>
                                                        <td>
                                                            <span class="badge bg-secondary">
                                                                <?php 
                                                                echo match($eval['evaluation_type']) {
                                                                    'quiz' => 'اختبار',
                                                                    'assignment' => 'واجب',
                                                                    'project' => 'مشروع',
                                                                    'final' => 'نهائي',
                                                                    'participation' => 'مشاركة',
                                                                    default => $eval['evaluation_type']
                                                                };
                                                                ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <?php if ($eval['score'] !== null): ?>
                                                                <strong><?php echo $eval['score']; ?></strong> / <?php echo $eval['max_score']; ?>
                                                            <?php else: ?>
                                                                <span class="text-muted">لم يتم التقييم</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($eval['score'] !== null): ?>
                                                                <?php $percentage = round(($eval['score'] / $eval['max_score']) * 100, 1); ?>
                                                                <span class="badge bg-<?php echo $percentage >= 80 ? 'success' : ($percentage >= 60 ? 'warning' : 'danger'); ?>">
                                                                    <?php echo $percentage; ?>%
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="text-muted">-</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($eval['grade']): ?>
                                                                <span class="badge bg-primary"><?php echo $eval['grade']; ?></span>
                                                            <?php else: ?>
                                                                <span class="text-muted">-</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($eval['graded_at']): ?>
                                                                <?php echo date('d/m/Y', strtotime($eval['graded_at'])); ?>
                                                            <?php else: ?>
                                                                <span class="text-muted">معلق</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
function exportStudentReport() {
    let report = 'تقرير الطالب: <?php echo addslashes($student["name"]); ?>\n';
    report += '=================================\n\n';
    report += 'المعلومات الأساسية:\n';
    report += 'الاسم: <?php echo addslashes($student["name"]); ?>\n';
    report += 'البريد الإلكتروني: <?php echo addslashes($student["email"]); ?>\n';
    report += 'معدل الحضور: <?php echo $attendance_rate; ?>%\n';
    report += 'المعدل العام: <?php echo number_format($avg_score, 1); ?>%\n\n';
    
    report += 'الدورات المسجلة:\n';
    report += '================\n';
    <?php foreach ($student_courses as $course): ?>
        report += '- <?php echo addslashes($course["course_title"]); ?> (<?php echo $course["enrollment_status"]; ?>)\n';
    <?php endforeach; ?>
    
    report += '\nسجل الحضور:\n';
    report += '============\n';
    <?php foreach ($attendance_records as $record): ?>
        report += '<?php echo date("d/m/Y", strtotime($record["session_date"])); ?> - <?php echo addslashes($record["title"]); ?> - <?php echo $record["attendance_status"] ?? "غير مسجل"; ?>\n';
    <?php endforeach; ?>
    
    const blob = new Blob([report], { type: 'text/plain;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'تقرير_الطالب_<?php echo preg_replace("/[^a-zA-Z0-9]/", "_", $student["name"]); ?>.txt');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>

<?php include '../includes/footer.php'; ?>
