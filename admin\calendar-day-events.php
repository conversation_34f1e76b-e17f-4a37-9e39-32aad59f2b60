<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('admin');

$date = isset($_GET['date']) ? $_GET['date'] : '';

if (!$date || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
    echo '<div class="alert alert-danger">تاريخ غير صحيح</div>';
    exit;
}

// Get all events for this date
$events_stmt = $conn->prepare("
    SELECT cs.id, cs.title, cs.session_date as event_date, cs.start_time, cs.end_time, 
           cs.location, c.title as course_title, c.id as course_id,
           'session' as event_type, c.instructor_id,
           u.name as instructor_name,
           COUNT(DISTINCT e.user_id) as enrolled_students,
           COUNT(DISTINCT a.user_id) as attended_students
    FROM course_sessions cs
    JOIN courses c ON cs.course_id = c.id
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN enrollments e ON c.id = e.course_id AND e.status IN ('active', 'completed')
    LEFT JOIN attendance a ON cs.id = a.session_id AND a.status = 'present'
    WHERE cs.session_date = ?
    GROUP BY cs.id
    
    UNION ALL
    
    SELECT c.id, CONCAT('بداية دورة: ', c.title) as title, c.start_date as event_date, 
           '09:00:00' as start_time, '17:00:00' as end_time,
           c.location, c.title as course_title, c.id as course_id,
           'course_start' as event_type, c.instructor_id,
           u.name as instructor_name,
           COUNT(DISTINCT e.user_id) as enrolled_students,
           0 as attended_students
    FROM courses c
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN enrollments e ON c.id = e.course_id AND e.status IN ('active', 'completed')
    WHERE c.start_date = ? AND c.status = 'active'
    GROUP BY c.id
    
    ORDER BY start_time
");
$events_stmt->execute([$date, $date]);
$events = $events_stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($events)) {
    echo '<div class="text-center py-4">';
    echo '<i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>';
    echo '<h5>لا توجد أحداث</h5>';
    echo '<p class="text-muted">لا توجد جلسات أو دورات مجدولة في ' . date('d/m/Y', strtotime($date)) . '</p>';
    echo '</div>';
    exit;
}

// Format date for display
$formatted_date = date('l، d F Y', strtotime($date));
$arabic_date = str_replace(
    ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
    ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'],
    $formatted_date
);
$arabic_date = str_replace(
    ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
    ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
    $arabic_date
);
?>

<div class="text-center mb-4">
    <h4 class="text-primary">
        <i class="fas fa-calendar-day me-2"></i>
        أحداث يوم <?php echo $arabic_date; ?>
    </h4>
    <p class="text-muted">إجمالي الأحداث: <?php echo count($events); ?></p>
</div>

<div class="timeline">
    <?php foreach ($events as $event): ?>
        <div class="timeline-item mb-4">
            <div class="row">
                <div class="col-md-3 text-end">
                    <div class="timeline-time">
                        <strong><?php echo date('H:i', strtotime($event['start_time'])); ?></strong>
                        <br>
                        <small class="text-muted">
                            <?php echo date('H:i', strtotime($event['end_time'])); ?>
                        </small>
                    </div>
                </div>
                <div class="col-md-9">
                    <div class="card border-start border-4 <?php echo $event['event_type'] === 'course_start' ? 'border-warning' : 'border-primary'; ?>">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-0">
                                    <?php if ($event['event_type'] === 'course_start'): ?>
                                        <i class="fas fa-star text-warning me-2"></i>
                                    <?php else: ?>
                                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                                    <?php endif; ?>
                                    <?php echo htmlspecialchars($event['title']); ?>
                                </h6>
                                <span class="badge bg-<?php echo $event['event_type'] === 'course_start' ? 'warning text-dark' : 'primary'; ?>">
                                    <?php echo $event['event_type'] === 'course_start' ? 'بداية دورة' : 'جلسة'; ?>
                                </span>
                            </div>
                            
                            <p class="card-text mb-2">
                                <strong>الدورة:</strong> <?php echo htmlspecialchars($event['course_title']); ?>
                            </p>
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        المدرب: <?php echo htmlspecialchars($event['instructor_name'] ?? 'غير محدد'); ?>
                                    </small>
                                </div>
                                <div class="col-sm-6">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        المكان: <?php echo htmlspecialchars($event['location'] ?? 'غير محدد'); ?>
                                    </small>
                                </div>
                            </div>
                            
                            <?php if ($event['event_type'] === 'session'): ?>
                                <div class="row mt-2">
                                    <div class="col-sm-6">
                                        <small class="text-muted">
                                            <i class="fas fa-users me-1"></i>
                                            المسجلين: <?php echo $event['enrolled_students']; ?>
                                        </small>
                                    </div>
                                    <div class="col-sm-6">
                                        <small class="text-muted">
                                            <i class="fas fa-check me-1"></i>
                                            الحاضرين: <?php echo $event['attended_students']; ?>
                                        </small>
                                    </div>
                                </div>
                                
                                <?php if ($event['enrolled_students'] > 0): ?>
                                    <div class="mt-2">
                                        <div class="progress" style="height: 5px;">
                                            <div class="progress-bar" style="width: <?php echo round(($event['attended_students'] / $event['enrolled_students']) * 100); ?>%"></div>
                                        </div>
                                        <small class="text-muted">معدل الحضور: <?php echo round(($event['attended_students'] / $event['enrolled_students']) * 100); ?>%</small>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-users me-1"></i>
                                        الطلاب المسجلين: <?php echo $event['enrolled_students']; ?>
                                    </small>
                                </div>
                            <?php endif; ?>
                            
                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                        onclick="showEventDetails(<?php echo $event['id']; ?>, '<?php echo $event['event_type']; ?>')">
                                    <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                </button>
                                
                                <?php if ($event['event_type'] === 'session'): ?>
                                    <a href="session-edit.php?id=<?php echo $event['id']; ?>" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-edit me-1"></i>تعديل
                                    </a>
                                    <a href="attendance.php?session_id=<?php echo $event['id']; ?>" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-check me-1"></i>الحضور
                                    </a>
                                <?php else: ?>
                                    <a href="course-edit.php?id=<?php echo $event['id']; ?>" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-edit me-1"></i>تعديل
                                    </a>
                                    <a href="course-manage.php?id=<?php echo $event['id']; ?>" class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-cog me-1"></i>إدارة
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<style>
.timeline-item {
    position: relative;
}

.timeline-time {
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.card {
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .timeline-time {
        text-align: start;
        margin-bottom: 10px;
    }
}
</style>
