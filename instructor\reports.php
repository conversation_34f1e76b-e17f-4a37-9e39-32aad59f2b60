<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('instructor');

$instructor_id = $_SESSION['user_id'];

// Get date range from request
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$course_filter = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;

// Get instructor's courses
$courses_stmt = $conn->prepare("
    SELECT id, title FROM courses 
    WHERE instructor_id = ? AND status = 'active'
    ORDER BY title
");
$courses_stmt->execute([$instructor_id]);
$courses = $courses_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get overview statistics
function getInstructorStats($conn, $instructor_id, $start_date, $end_date, $course_filter = 0) {
    $stats = [];
    
    $course_condition = $course_filter > 0 ? "AND c.id = $course_filter" : "";
    
    // Total students
    $stmt = $conn->prepare("
        SELECT COUNT(DISTINCT e.user_id) 
        FROM enrollments e
        JOIN courses c ON e.course_id = c.id
        WHERE c.instructor_id = ? AND e.created_at BETWEEN ? AND ? $course_condition
    ");
    $stmt->execute([$instructor_id, $start_date, $end_date . ' 23:59:59']);
    $stats['total_students'] = $stmt->fetchColumn();
    
    // Total sessions
    $stmt = $conn->prepare("
        SELECT COUNT(*) 
        FROM course_sessions cs
        JOIN courses c ON cs.course_id = c.id
        WHERE c.instructor_id = ? AND cs.session_date BETWEEN ? AND ? $course_condition
    ");
    $stmt->execute([$instructor_id, $start_date, $end_date]);
    $stats['total_sessions'] = $stmt->fetchColumn();
    
    // Total evaluations
    $stmt = $conn->prepare("
        SELECT COUNT(*) 
        FROM evaluations ev
        JOIN courses c ON ev.course_id = c.id
        WHERE ev.instructor_id = ? AND ev.created_at BETWEEN ? AND ? $course_condition
    ");
    $stmt->execute([$instructor_id, $start_date, $end_date . ' 23:59:59']);
    $stats['total_evaluations'] = $stmt->fetchColumn();
    
    // Average attendance rate
    $stmt = $conn->prepare("
        SELECT 
            COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count,
            COUNT(a.id) as total_attendance
        FROM attendance a
        JOIN course_sessions cs ON a.session_id = cs.id
        JOIN courses c ON cs.course_id = c.id
        WHERE c.instructor_id = ? AND cs.session_date BETWEEN ? AND ? $course_condition
    ");
    $stmt->execute([$instructor_id, $start_date, $end_date]);
    $attendance_data = $stmt->fetch();
    $stats['attendance_rate'] = $attendance_data['total_attendance'] > 0 ? 
        round(($attendance_data['present_count'] / $attendance_data['total_attendance']) * 100, 1) : 0;
    
    return $stats;
}

// Get course performance data
function getCoursePerformance($conn, $instructor_id, $start_date, $end_date, $course_filter = 0) {
    $course_condition = $course_filter > 0 ? "AND c.id = $course_filter" : "";
    
    $stmt = $conn->prepare("
        SELECT c.id, c.title,
               COUNT(DISTINCT e.user_id) as enrolled_students,
               COUNT(DISTINCT cs.id) as total_sessions,
               COUNT(DISTINCT CASE WHEN a.status = 'present' THEN a.user_id END) as active_students,
               AVG(ev.score) as avg_score,
               COUNT(DISTINCT ev.id) as total_evaluations
        FROM courses c
        LEFT JOIN enrollments e ON c.id = e.course_id AND e.created_at BETWEEN ? AND ?
        LEFT JOIN course_sessions cs ON c.id = cs.course_id AND cs.session_date BETWEEN ? AND ?
        LEFT JOIN attendance a ON cs.id = a.session_id
        LEFT JOIN evaluations ev ON c.id = ev.course_id AND ev.created_at BETWEEN ? AND ?
        WHERE c.instructor_id = ? AND c.status = 'active' $course_condition
        GROUP BY c.id, c.title
        ORDER BY enrolled_students DESC
    ");
    $stmt->execute([$start_date, $end_date . ' 23:59:59', $start_date, $end_date, $start_date, $end_date . ' 23:59:59', $instructor_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get student performance data
function getStudentPerformance($conn, $instructor_id, $start_date, $end_date, $course_filter = 0) {
    $course_condition = $course_filter > 0 ? "AND c.id = $course_filter" : "";
    
    $stmt = $conn->prepare("
        SELECT u.id, u.name, u.email, c.title as course_title,
               COUNT(DISTINCT cs.id) as total_sessions,
               COUNT(CASE WHEN a.status = 'present' THEN 1 END) as attended_sessions,
               AVG(ev.score) as avg_score,
               COUNT(DISTINCT ev.id) as total_evaluations
        FROM users u
        JOIN enrollments e ON u.id = e.user_id
        JOIN courses c ON e.course_id = c.id
        LEFT JOIN course_sessions cs ON c.id = cs.course_id AND cs.session_date BETWEEN ? AND ?
        LEFT JOIN attendance a ON cs.id = a.session_id AND a.user_id = u.id
        LEFT JOIN evaluations ev ON c.id = ev.course_id AND ev.user_id = u.id AND ev.created_at BETWEEN ? AND ?
        WHERE c.instructor_id = ? AND e.status IN ('active', 'completed') $course_condition
        GROUP BY u.id, u.name, u.email, c.title
        ORDER BY avg_score DESC, attended_sessions DESC
        LIMIT 20
    ");
    $stmt->execute([$start_date, $end_date, $start_date, $end_date . ' 23:59:59', $instructor_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Generate data
$stats = getInstructorStats($conn, $instructor_id, $start_date, $end_date, $course_filter);
$course_performance = getCoursePerformance($conn, $instructor_id, $start_date, $end_date, $course_filter);
$student_performance = getStudentPerformance($conn, $instructor_id, $start_date, $end_date, $course_filter);

$page_title = 'تقاريري';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                دوراتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="sessions.php">
                                <i class="fas fa-calendar-alt"></i>
                                الجلسات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate"></i>
                                الطلاب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="reports.php">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">تقاريري</h1>
                    <button type="button" class="btn btn-primary" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>تصدير التقرير
                    </button>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="course_id" class="form-label">الدورة</label>
                                <select class="form-select" id="course_id" name="course_id">
                                    <option value="">جميع الدورات</option>
                                    <?php foreach ($courses as $course): ?>
                                        <option value="<?php echo $course['id']; ?>" 
                                                <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($course['title']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Overview Statistics -->
                <div class="row g-4 mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الطلاب</h6>
                                        <h2 class="fw-bold"><?php echo number_format($stats['total_students']); ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-graduate fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي الجلسات</h6>
                                        <h2 class="fw-bold"><?php echo number_format($stats['total_sessions']); ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">إجمالي التقييمات</h6>
                                        <h2 class="fw-bold"><?php echo number_format($stats['total_evaluations']); ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clipboard-check fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">معدل الحضور</h6>
                                        <h2 class="fw-bold"><?php echo $stats['attendance_rate']; ?>%</h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course Performance -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-graduation-cap me-2"></i>
                            أداء الدورات
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($course_performance)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h5>لا توجد بيانات</h5>
                                <p class="text-muted">لا توجد بيانات أداء للفترة المحددة</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الدورة</th>
                                            <th>الطلاب المسجلين</th>
                                            <th>الطلاب النشطين</th>
                                            <th>إجمالي الجلسات</th>
                                            <th>المعدل العام</th>
                                            <th>التقييمات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($course_performance as $course): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($course['title']); ?></strong>
                                                </td>
                                                <td><?php echo number_format($course['enrolled_students']); ?></td>
                                                <td><?php echo number_format($course['active_students']); ?></td>
                                                <td><?php echo number_format($course['total_sessions']); ?></td>
                                                <td>
                                                    <?php if ($course['avg_score']): ?>
                                                        <span class="badge bg-<?php echo $course['avg_score'] >= 80 ? 'success' : ($course['avg_score'] >= 60 ? 'warning' : 'danger'); ?>">
                                                            <?php echo number_format($course['avg_score'], 1); ?>%
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">لا يوجد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo number_format($course['total_evaluations']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Student Performance -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-graduate me-2"></i>
                            أداء الطلاب (أفضل 20 طالب)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($student_performance)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                                <h5>لا توجد بيانات</h5>
                                <p class="text-muted">لا توجد بيانات طلاب للفترة المحددة</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الطالب</th>
                                            <th>الدورة</th>
                                            <th>الحضور</th>
                                            <th>معدل الحضور</th>
                                            <th>المعدل</th>
                                            <th>التقييمات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($student_performance as $student): ?>
                                            <?php
                                            $attendance_rate = $student['total_sessions'] > 0 ? 
                                                round(($student['attended_sessions'] / $student['total_sessions']) * 100, 1) : 0;
                                            ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($student['name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                                                </td>
                                                <td><?php echo htmlspecialchars($student['course_title']); ?></td>
                                                <td>
                                                    <?php echo $student['attended_sessions']; ?>/<?php echo $student['total_sessions']; ?>
                                                </td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar bg-<?php echo $attendance_rate >= 80 ? 'success' : ($attendance_rate >= 60 ? 'warning' : 'danger'); ?>" 
                                                             style="width: <?php echo $attendance_rate; ?>%">
                                                            <?php echo $attendance_rate; ?>%
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($student['avg_score']): ?>
                                                        <span class="badge bg-<?php echo $student['avg_score'] >= 80 ? 'success' : ($student['avg_score'] >= 60 ? 'warning' : 'danger'); ?>">
                                                            <?php echo number_format($student['avg_score'], 1); ?>%
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">لا يوجد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo number_format($student['total_evaluations']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
function exportReport() {
    // Create a comprehensive report
    let report = 'تقرير أداء المدرب\n';
    report += '==================\n\n';
    report += `الفترة: من ${document.getElementById('start_date').value} إلى ${document.getElementById('end_date').value}\n\n`;
    
    report += 'الإحصائيات العامة:\n';
    report += '=================\n';
    report += `إجمالي الطلاب: ${<?php echo $stats['total_students']; ?>}\n`;
    report += `إجمالي الجلسات: ${<?php echo $stats['total_sessions']; ?>}\n`;
    report += `إجمالي التقييمات: ${<?php echo $stats['total_evaluations']; ?>}\n`;
    report += `معدل الحضور: ${<?php echo $stats['attendance_rate']; ?>}%\n\n`;
    
    report += 'أداء الدورات:\n';
    report += '=============\n';
    <?php foreach ($course_performance as $course): ?>
        report += '<?php echo addslashes($course["title"]); ?>\n';
        report += 'الطلاب المسجلين: <?php echo $course["enrolled_students"]; ?>\n';
        report += 'الطلاب النشطين: <?php echo $course["active_students"]; ?>\n';
        report += 'المعدل العام: <?php echo $course["avg_score"] ? number_format($course["avg_score"], 1) : "0"; ?>%\n\n';
    <?php endforeach; ?>
    
    const blob = new Blob([report], { type: 'text/plain;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'تقرير_أداء_المدرب.txt');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>

<?php include '../includes/footer.php'; ?>
