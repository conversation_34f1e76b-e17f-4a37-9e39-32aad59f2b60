<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('admin');

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = sanitize_input($_POST['title']);
    $description = sanitize_input($_POST['description']);
    $short_description = sanitize_input($_POST['short_description']);
    $category_id = intval($_POST['category_id']);
    $instructor_id = intval($_POST['instructor_id']);
    $level = sanitize_input($_POST['level']);
    $duration_hours = intval($_POST['duration_hours']);
    $max_students = intval($_POST['max_students']);
    $price = floatval($_POST['price']);
    $status = sanitize_input($_POST['status']);
    $featured = isset($_POST['featured']) ? 1 : 0;
    $prerequisites = sanitize_input($_POST['prerequisites']);
    $objectives = sanitize_input($_POST['objectives']);
    $syllabus = sanitize_input($_POST['syllabus']);
    $start_date = sanitize_input($_POST['start_date']);
    $end_date = sanitize_input($_POST['end_date']);
    $location = sanitize_input($_POST['location']);

    // Handle image upload
    $image_path = null;
    if (isset($_FILES['course_image']) && $_FILES['course_image']['error'] === UPLOAD_ERR_OK) {
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
        $upload_result = uploadFile($_FILES['course_image'], 'uploads/courses/', $allowed_types, 5 * 1024 * 1024);

        if ($upload_result['success']) {
            $image_path = $upload_result['file_path'];
        } else {
            $error = $upload_result['error'];
        }
    }

    if (empty($title) || empty($description) || empty($short_description)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!empty($start_date) && !empty($end_date) && strtotime($start_date) >= strtotime($end_date)) {
        $error = 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية';
    } else {
        try {
            $slug = generateSlug($title);
            
            // Check if slug already exists
            $check_stmt = $conn->prepare("SELECT id FROM courses WHERE slug = ?");
            $check_stmt->execute([$slug]);
            if ($check_stmt->fetch()) {
                $slug = $slug . '-' . time();
            }
            
            $stmt = $conn->prepare("
                INSERT INTO courses (title, description, short_description, slug, category_id, instructor_id,
                                   level, duration_hours, max_students, price, status, featured, prerequisites,
                                   objectives, syllabus, start_date, end_date, location, image)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $start_date = !empty($start_date) ? $start_date : null;
            $end_date = !empty($end_date) ? $end_date : null;
            $instructor_id = $instructor_id > 0 ? $instructor_id : null;
            $category_id = $category_id > 0 ? $category_id : null;
            
            if ($stmt->execute([$title, $description, $short_description, $slug, $category_id, $instructor_id,
                               $level, $duration_hours, $max_students, $price, $status, $featured,
                               $prerequisites, $objectives, $syllabus, $start_date, $end_date, $location, $image_path])) {
                
                $course_id = $conn->lastInsertId();
                
                // Log activity
                $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, resource_type, resource_id, description, ip_address) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$_SESSION['user_id'], 'create', 'course', $course_id, "إضافة دورة جديدة: $title", $_SERVER['REMOTE_ADDR']]);
                
                $_SESSION['flash_message'] = 'تم إضافة الدورة بنجاح';
                $_SESSION['flash_type'] = 'success';
                redirect('courses.php');
            } else {
                $error = 'حدث خطأ في إضافة الدورة';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى';
        }
    }
}

// Get categories
$categories_stmt = $conn->prepare("SELECT id, name FROM categories WHERE status = 'active' ORDER BY name");
$categories_stmt->execute();
$categories = $categories_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get instructors
$instructors_stmt = $conn->prepare("SELECT id, name FROM users WHERE role = 'instructor' AND status = 'active' ORDER BY name");
$instructors_stmt->execute();
$instructors = $instructors_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'إضافة دورة جديدة';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                إدارة الدورات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="enrollments.php">
                                <i class="fas fa-user-graduate"></i>
                                التسجيلات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="categories.php">
                                <i class="fas fa-tags"></i>
                                التصنيفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="calendar.php">
                                <i class="fas fa-calendar"></i>
                                التقويم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">إضافة دورة جديدة</h1>
                    <a href="courses.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للدورات
                    </a>
                </div>

                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <div class="row g-4">
                        <!-- Basic Information -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        المعلومات الأساسية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <label for="title" class="form-label">عنوان الدورة <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="title" name="title" 
                                                   value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>" 
                                                   required>
                                        </div>

                                        <div class="col-12">
                                            <label for="short_description" class="form-label">الوصف المختصر <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="short_description" name="short_description" 
                                                      rows="3" maxlength="500" required><?php echo isset($_POST['short_description']) ? htmlspecialchars($_POST['short_description']) : ''; ?></textarea>
                                            <div class="form-text">حد أقصى 500 حرف</div>
                                        </div>

                                        <div class="col-12">
                                            <label for="description" class="form-label">الوصف التفصيلي <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="description" name="description" 
                                                      rows="6" required><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                                        </div>

                                        <div class="col-md-6">
                                            <label for="category_id" class="form-label">التصنيف</label>
                                            <select class="form-select" id="category_id" name="category_id">
                                                <option value="">اختر التصنيف</option>
                                                <?php foreach ($categories as $category): ?>
                                                    <option value="<?php echo $category['id']; ?>" 
                                                            <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($category['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="col-md-6">
                                            <label for="instructor_id" class="form-label">المدرب</label>
                                            <select class="form-select" id="instructor_id" name="instructor_id">
                                                <option value="">اختر المدرب</option>
                                                <?php foreach ($instructors as $instructor): ?>
                                                    <option value="<?php echo $instructor['id']; ?>" 
                                                            <?php echo (isset($_POST['instructor_id']) && $_POST['instructor_id'] == $instructor['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($instructor['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Details -->
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-cog me-2"></i>
                                        تفاصيل الدورة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <label for="level" class="form-label">المستوى</label>
                                            <select class="form-select" id="level" name="level">
                                                <option value="beginner" <?php echo (isset($_POST['level']) && $_POST['level'] == 'beginner') ? 'selected' : ''; ?>>مبتدئ</option>
                                                <option value="intermediate" <?php echo (isset($_POST['level']) && $_POST['level'] == 'intermediate') ? 'selected' : ''; ?>>متوسط</option>
                                                <option value="advanced" <?php echo (isset($_POST['level']) && $_POST['level'] == 'advanced') ? 'selected' : ''; ?>>متقدم</option>
                                            </select>
                                        </div>

                                        <div class="col-12">
                                            <label for="duration_hours" class="form-label">المدة (ساعات)</label>
                                            <input type="number" class="form-control" id="duration_hours" name="duration_hours" 
                                                   value="<?php echo isset($_POST['duration_hours']) ? $_POST['duration_hours'] : '40'; ?>" 
                                                   min="1" max="500">
                                        </div>

                                        <div class="col-12">
                                            <label for="max_students" class="form-label">العدد الأقصى للطلاب</label>
                                            <input type="number" class="form-control" id="max_students" name="max_students" 
                                                   value="<?php echo isset($_POST['max_students']) ? $_POST['max_students'] : '30'; ?>" 
                                                   min="1" max="100">
                                        </div>

                                        <div class="col-12">
                                            <label for="price" class="form-label">السعر (ريال)</label>
                                            <input type="number" class="form-control" id="price" name="price"
                                                   value="<?php echo isset($_POST['price']) ? $_POST['price'] : '0'; ?>"
                                                   min="0" step="0.01">
                                        </div>

                                        <div class="col-12">
                                            <label for="course_image" class="form-label">صورة الدورة</label>
                                            <input type="file" class="form-control" id="course_image" name="course_image"
                                                   accept="image/*">
                                            <div class="form-text">اختر صورة تمثل الدورة (اختياري)</div>
                                        </div>

                                        <div class="col-12">
                                            <label for="status" class="form-label">الحالة</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="draft" <?php echo (isset($_POST['status']) && $_POST['status'] == 'draft') ? 'selected' : 'selected'; ?>>مسودة</option>
                                                <option value="active" <?php echo (isset($_POST['status']) && $_POST['status'] == 'active') ? 'selected' : ''; ?>>نشط</option>
                                                <option value="archived" <?php echo (isset($_POST['status']) && $_POST['status'] == 'archived') ? 'selected' : ''; ?>>مؤرشف</option>
                                            </select>
                                        </div>

                                        <div class="col-12">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="featured" name="featured" 
                                                       <?php echo (isset($_POST['featured']) && $_POST['featured']) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="featured">
                                                    دورة مميزة
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Content -->
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-book me-2"></i>
                                        محتوى الدورة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="prerequisites" class="form-label">المتطلبات المسبقة</label>
                                            <textarea class="form-control" id="prerequisites" name="prerequisites" 
                                                      rows="4" placeholder="المهارات أو المعرفة المطلوبة قبل بدء الدورة..."><?php echo isset($_POST['prerequisites']) ? htmlspecialchars($_POST['prerequisites']) : ''; ?></textarea>
                                        </div>

                                        <div class="col-md-6">
                                            <label for="objectives" class="form-label">أهداف الدورة</label>
                                            <textarea class="form-control" id="objectives" name="objectives" 
                                                      rows="4" placeholder="ما سيتعلمه الطالب في هذه الدورة..."><?php echo isset($_POST['objectives']) ? htmlspecialchars($_POST['objectives']) : ''; ?></textarea>
                                        </div>

                                        <div class="col-12">
                                            <label for="syllabus" class="form-label">منهج الدورة</label>
                                            <textarea class="form-control" id="syllabus" name="syllabus" 
                                                      rows="6" placeholder="المواضيع والفصول التي ستغطيها الدورة..."><?php echo isset($_POST['syllabus']) ? htmlspecialchars($_POST['syllabus']) : ''; ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Schedule & Location -->
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-calendar me-2"></i>
                                        الجدولة والمكان
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label for="start_date" class="form-label">تاريخ البداية</label>
                                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                                   value="<?php echo isset($_POST['start_date']) ? $_POST['start_date'] : ''; ?>">
                                        </div>

                                        <div class="col-md-4">
                                            <label for="end_date" class="form-label">تاريخ النهاية</label>
                                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                                   value="<?php echo isset($_POST['end_date']) ? $_POST['end_date'] : ''; ?>">
                                        </div>

                                        <div class="col-md-4">
                                            <label for="location" class="form-label">المكان</label>
                                            <input type="text" class="form-control" id="location" name="location" 
                                                   value="<?php echo isset($_POST['location']) ? htmlspecialchars($_POST['location']) : ''; ?>" 
                                                   placeholder="قاعة المحاضرات الأولى">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <a href="courses.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>إلغاء
                                        </a>
                                        <div>
                                            <button type="submit" name="status" value="draft" class="btn btn-outline-primary me-2">
                                                <i class="fas fa-save me-2"></i>حفظ كمسودة
                                            </button>
                                            <button type="submit" name="status" value="active" class="btn btn-primary">
                                                <i class="fas fa-check me-2"></i>نشر الدورة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>
</div>

<script>
// Validate dates
document.getElementById('end_date').addEventListener('change', function() {
    const startDate = document.getElementById('start_date').value;
    const endDate = this.value;
    
    if (startDate && endDate && startDate >= endDate) {
        this.setCustomValidity('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('start_date').addEventListener('change', function() {
    const endDateInput = document.getElementById('end_date');
    endDateInput.dispatchEvent(new Event('change'));
});

// Character counter for short description
document.getElementById('short_description').addEventListener('input', function() {
    const maxLength = 500;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;
    
    let helpText = this.parentNode.querySelector('.form-text');
    if (!helpText) {
        helpText = document.createElement('div');
        helpText.className = 'form-text';
        this.parentNode.appendChild(helpText);
    }
    
    if (remaining < 50) {
        helpText.className = 'form-text text-warning';
    } else if (remaining < 0) {
        helpText.className = 'form-text text-danger';
    } else {
        helpText.className = 'form-text';
    }
    
    helpText.textContent = `حد أقصى 500 حرف (متبقي: ${remaining})`;
});
</script>

<?php include '../includes/footer.php'; ?>