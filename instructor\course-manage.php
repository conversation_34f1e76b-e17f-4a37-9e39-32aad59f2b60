<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('instructor');

$instructor_id = $_SESSION['user_id'];
$course_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$course_id) {
    header('Location: courses.php');
    exit;
}

// Verify instructor owns this course
$course_stmt = $conn->prepare("
    SELECT c.*, cat.name as category_name,
           COUNT(DISTINCT e.user_id) as enrolled_students,
           COUNT(DISTINCT cs.id) as total_sessions,
           AVG(ev.score) as avg_score
    FROM courses c
    LEFT JOIN categories cat ON c.category_id = cat.id
    LEFT JOIN enrollments e ON c.id = e.course_id
    LEFT JOIN course_sessions cs ON c.id = cs.course_id
    LEFT JOIN evaluations ev ON c.id = ev.course_id
    WHERE c.id = ? AND c.instructor_id = ?
    GROUP BY c.id
");
$course_stmt->execute([$course_id, $instructor_id]);
$course = $course_stmt->fetch(PDO::FETCH_ASSOC);

if (!$course) {
    header('Location: courses.php');
    exit;
}

// Get course sessions
$sessions_stmt = $conn->prepare("
    SELECT cs.*, 
           COUNT(a.id) as total_attendance,
           COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count
    FROM course_sessions cs
    LEFT JOIN attendance a ON cs.id = a.session_id
    WHERE cs.course_id = ?
    GROUP BY cs.id
    ORDER BY cs.session_date DESC, cs.start_time DESC
");
$sessions_stmt->execute([$course_id]);
$sessions = $sessions_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get enrolled students
$students_stmt = $conn->prepare("
    SELECT u.*, e.status as enrollment_status, e.created_at as enrollment_date,
           COUNT(CASE WHEN a.status = 'present' THEN 1 END) as attended_sessions,
           COUNT(cs.id) as total_sessions,
           AVG(ev.score) as avg_score
    FROM users u
    JOIN enrollments e ON u.id = e.user_id
    LEFT JOIN course_sessions cs ON e.course_id = cs.course_id
    LEFT JOIN attendance a ON cs.id = a.session_id AND a.user_id = u.id
    LEFT JOIN evaluations ev ON e.course_id = ev.course_id AND ev.user_id = u.id
    WHERE e.course_id = ? AND u.role = 'student'
    GROUP BY u.id
    ORDER BY e.created_at DESC
");
$students_stmt->execute([$course_id]);
$students = $students_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get course materials
$materials_stmt = $conn->prepare("
    SELECT * FROM course_materials 
    WHERE course_id = ? 
    ORDER BY sort_order ASC, created_at DESC
");
$materials_stmt->execute([$course_id]);
$materials = $materials_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get evaluations
$evaluations_stmt = $conn->prepare("
    SELECT ev.*, COUNT(CASE WHEN ev.score IS NOT NULL THEN 1 END) as graded_count,
           COUNT(ev.user_id) as total_submissions
    FROM evaluations ev
    WHERE ev.course_id = ? AND ev.instructor_id = ?
    GROUP BY ev.title, ev.evaluation_type, ev.evaluation_date
    ORDER BY ev.created_at DESC
");
$evaluations_stmt->execute([$course_id, $instructor_id]);
$evaluations = $evaluations_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'إدارة الدورة - ' . $course['title'];
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                دوراتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="sessions.php">
                                <i class="fas fa-calendar-alt"></i>
                                الجلسات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate"></i>
                                الطلاب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="evaluations.php">
                                <i class="fas fa-clipboard-check"></i>
                                التقييمات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 fw-bold">إدارة الدورة</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="courses.php">دوراتي</a></li>
                                <li class="breadcrumb-item active"><?php echo htmlspecialchars($course['title']); ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="../admin/course-edit.php?id=<?php echo $course_id; ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>تعديل الدورة
                        </a>
                        <a href="courses.php" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>العودة
                        </a>
                    </div>
                </div>

                <!-- Course Overview -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="fw-bold mb-2"><?php echo htmlspecialchars($course['title']); ?></h4>
                                <p class="text-muted mb-3"><?php echo htmlspecialchars($course['description']); ?></p>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <small class="text-muted">التصنيف:</small>
                                        <p class="mb-2"><?php echo htmlspecialchars($course['category_name'] ?? 'غير محدد'); ?></p>
                                    </div>
                                    <div class="col-sm-6">
                                        <small class="text-muted">المدة:</small>
                                        <p class="mb-2"><?php echo $course['duration_hours']; ?> ساعة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h5 class="fw-bold text-primary"><?php echo $course['enrolled_students']; ?></h5>
                                        <small class="text-muted">طالب مسجل</small>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="fw-bold text-success"><?php echo $course['total_sessions']; ?></h5>
                                        <small class="text-muted">جلسة</small>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <span class="badge bg-<?php 
                                        echo match($course['status']) {
                                            'active' => 'success',
                                            'pending' => 'warning',
                                            'inactive' => 'secondary',
                                            default => 'primary'
                                        };
                                    ?> fs-6">
                                        <?php 
                                        echo match($course['status']) {
                                            'active' => 'نشط',
                                            'pending' => 'معلق',
                                            'inactive' => 'غير نشط',
                                            default => $course['status']
                                        };
                                        ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs mb-4" id="courseManageTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="students-tab" data-bs-toggle="tab" data-bs-target="#students" type="button">
                            <i class="fas fa-user-graduate me-2"></i>الطلاب (<?php echo count($students); ?>)
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="sessions-tab" data-bs-toggle="tab" data-bs-target="#sessions" type="button">
                            <i class="fas fa-calendar-alt me-2"></i>الجلسات (<?php echo count($sessions); ?>)
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials" type="button">
                            <i class="fas fa-file-alt me-2"></i>المواد (<?php echo count($materials); ?>)
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="evaluations-tab" data-bs-toggle="tab" data-bs-target="#evaluations" type="button">
                            <i class="fas fa-clipboard-check me-2"></i>التقييمات (<?php echo count($evaluations); ?>)
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="courseManageTabsContent">
                    <!-- Students Tab -->
                    <div class="tab-pane fade show active" id="students" role="tabpanel">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header">
                                <h5 class="mb-0">الطلاب المسجلين</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($students)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                                        <h6>لا يوجد طلاب مسجلين</h6>
                                        <p class="text-muted">لم يسجل أي طالب في هذه الدورة بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>الطالب</th>
                                                    <th>تاريخ التسجيل</th>
                                                    <th>الحضور</th>
                                                    <th>معدل الحضور</th>
                                                    <th>المعدل</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($students as $student): ?>
                                                    <?php
                                                    $attendance_rate = $student['total_sessions'] > 0 ? 
                                                        round(($student['attended_sessions'] / $student['total_sessions']) * 100, 1) : 0;
                                                    ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <?php if (isset($student['profile_image']) && $student['profile_image'] && file_exists('../' . $student['profile_image'])): ?>
                                                                    <img src="../<?php echo htmlspecialchars($student['profile_image']); ?>" 
                                                                         alt="صورة الطالب" 
                                                                         class="rounded-circle me-3" 
                                                                         style="width: 40px; height: 40px; object-fit: cover;">
                                                                <?php else: ?>
                                                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                                                                         style="width: 40px; height: 40px;">
                                                                        <i class="fas fa-user"></i>
                                                                    </div>
                                                                <?php endif; ?>
                                                                <div>
                                                                    <strong><?php echo htmlspecialchars($student['name']); ?></strong>
                                                                    <br><small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td><?php echo date('d/m/Y', strtotime($student['enrollment_date'])); ?></td>
                                                        <td><?php echo $student['attended_sessions']; ?>/<?php echo $student['total_sessions']; ?></td>
                                                        <td>
                                                            <div class="progress" style="height: 20px;">
                                                                <div class="progress-bar bg-<?php echo $attendance_rate >= 80 ? 'success' : ($attendance_rate >= 60 ? 'warning' : 'danger'); ?>" 
                                                                     style="width: <?php echo $attendance_rate; ?>%">
                                                                    <?php echo $attendance_rate; ?>%
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <?php if ($student['avg_score']): ?>
                                                                <span class="badge bg-<?php echo $student['avg_score'] >= 80 ? 'success' : ($student['avg_score'] >= 60 ? 'warning' : 'danger'); ?>">
                                                                    <?php echo number_format($student['avg_score'], 1); ?>%
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="text-muted">لا يوجد</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-<?php 
                                                                echo match($student['enrollment_status']) {
                                                                    'active' => 'primary',
                                                                    'completed' => 'success',
                                                                    'pending' => 'warning',
                                                                    default => 'secondary'
                                                                };
                                                            ?>">
                                                                <?php 
                                                                echo match($student['enrollment_status']) {
                                                                    'active' => 'نشط',
                                                                    'completed' => 'مكتمل',
                                                                    'pending' => 'معلق',
                                                                    default => $student['enrollment_status']
                                                                };
                                                                ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <a href="student-profile.php?id=<?php echo $student['id']; ?>&course_id=<?php echo $course_id; ?>" 
                                                               class="btn btn-outline-primary btn-sm" title="عرض الملف">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Sessions Tab -->
                    <div class="tab-pane fade" id="sessions" role="tabpanel">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header">
                                <h5 class="mb-0">جلسات الدورة</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($sessions)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                        <h6>لا توجد جلسات</h6>
                                        <p class="text-muted">لم يتم إنشاء أي جلسات لهذه الدورة بعد</p>
                                        <p class="text-muted">يمكن للمدير إضافة الجلسات من لوحة التحكم</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>العنوان</th>
                                                    <th>التاريخ والوقت</th>
                                                    <th>المكان</th>
                                                    <th>الحضور</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($sessions as $session): ?>
                                                    <?php
                                                    $session_datetime = strtotime($session['session_date'] . ' ' . $session['start_time']);
                                                    $is_past = $session_datetime < time();
                                                    $is_today = date('Y-m-d') === $session['session_date'];
                                                    ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($session['title']); ?></strong>
                                                            <?php if ($session['description']): ?>
                                                                <br><small class="text-muted"><?php echo htmlspecialchars($session['description']); ?></small>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <strong><?php echo date('d/m/Y', strtotime($session['session_date'])); ?></strong>
                                                            <br><small><?php echo date('H:i', strtotime($session['start_time'])); ?> - <?php echo date('H:i', strtotime($session['end_time'])); ?></small>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($session['location'] ?? 'غير محدد'); ?></td>
                                                        <td>
                                                            <?php if ($session['total_attendance'] > 0): ?>
                                                                <span class="badge bg-info">
                                                                    <?php echo $session['present_count']; ?>/<?php echo $session['total_attendance']; ?>
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="badge bg-secondary">لا يوجد</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($is_past): ?>
                                                                <span class="badge bg-secondary">منتهية</span>
                                                            <?php elseif ($is_today): ?>
                                                                <span class="badge bg-warning">اليوم</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-primary">قادمة</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group btn-group-sm">
                                                                <a href="../admin/session-attendance.php?id=<?php echo $session['id']; ?>" 
                                                                   class="btn btn-outline-primary" title="تسجيل الحضور">
                                                                    <i class="fas fa-check"></i>
                                                                </a>
                                                                <a href="../admin/session-edit.php?id=<?php echo $session['id']; ?>" 
                                                                   class="btn btn-outline-secondary" title="تعديل">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Materials Tab -->
                    <div class="tab-pane fade" id="materials" role="tabpanel">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header">
                                <h5 class="mb-0">المواد التعليمية</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($materials)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                        <h6>لا توجد مواد تعليمية</h6>
                                        <p class="text-muted">لم يتم رفع أي مواد تعليمية لهذه الدورة بعد</p>
                                        <p class="text-muted">يمكن للمدير إضافة المواد التعليمية من لوحة التحكم</p>
                                    </div>
                                <?php else: ?>
                                    <div class="row g-3">
                                        <?php foreach ($materials as $material): ?>
                                            <div class="col-md-6 col-lg-4">
                                                <div class="card border">
                                                    <div class="card-body">
                                                        <div class="d-flex align-items-start">
                                                            <div class="me-3">
                                                                <i class="fas fa-<?php 
                                                                    echo match($material['file_type']) {
                                                                        'pdf' => 'file-pdf text-danger',
                                                                        'video' => 'file-video text-primary',
                                                                        'image' => 'file-image text-success',
                                                                        default => 'file-alt text-secondary'
                                                                    };
                                                                ?> fa-2x"></i>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <h6 class="card-title"><?php echo htmlspecialchars($material['title']); ?></h6>
                                                                <?php if ($material['description']): ?>
                                                                    <p class="card-text small text-muted"><?php echo htmlspecialchars($material['description']); ?></p>
                                                                <?php endif; ?>
                                                                <small class="text-muted">
                                                                    <?php echo formatFileSize($material['file_size']); ?> • 
                                                                    <?php echo date('d/m/Y', strtotime($material['created_at'])); ?>
                                                                </small>
                                                            </div>
                                                        </div>
                                                        <div class="mt-3">
                                                            <a href="../<?php echo htmlspecialchars($material['file_path']); ?>" 
                                                               class="btn btn-outline-primary btn-sm" target="_blank">
                                                                <i class="fas fa-download me-1"></i>تحميل
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Evaluations Tab -->
                    <div class="tab-pane fade" id="evaluations" role="tabpanel">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">التقييمات</h5>
                                <a href="evaluations.php?course_id=<?php echo $course_id; ?>" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus me-2"></i>إضافة تقييم
                                </a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($evaluations)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                                        <h6>لا توجد تقييمات</h6>
                                        <p class="text-muted">لم يتم إنشاء أي تقييمات لهذه الدورة بعد</p>
                                        <a href="evaluations.php?course_id=<?php echo $course_id; ?>" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>إضافة أول تقييم
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>التقييم</th>
                                                    <th>النوع</th>
                                                    <th>التاريخ</th>
                                                    <th>الدرجة العظمى</th>
                                                    <th>المشاركات</th>
                                                    <th>المقيمة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($evaluations as $eval): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($eval['title']); ?></strong>
                                                            <?php if ($eval['description']): ?>
                                                                <br><small class="text-muted"><?php echo htmlspecialchars($eval['description']); ?></small>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-secondary">
                                                                <?php 
                                                                echo match($eval['evaluation_type']) {
                                                                    'quiz' => 'اختبار',
                                                                    'assignment' => 'واجب',
                                                                    'project' => 'مشروع',
                                                                    'final' => 'نهائي',
                                                                    'participation' => 'مشاركة',
                                                                    default => $eval['evaluation_type']
                                                                };
                                                                ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <?php echo $eval['evaluation_date'] ? date('d/m/Y', strtotime($eval['evaluation_date'])) : 'غير محدد'; ?>
                                                        </td>
                                                        <td><?php echo $eval['max_score']; ?></td>
                                                        <td><?php echo $eval['total_submissions']; ?></td>
                                                        <td>
                                                            <span class="badge bg-<?php echo $eval['graded_count'] == $eval['total_submissions'] ? 'success' : 'warning'; ?>">
                                                                <?php echo $eval['graded_count']; ?>/<?php echo $eval['total_submissions']; ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <a href="evaluations.php?course_id=<?php echo $course_id; ?>&eval_id=<?php echo $eval['id']; ?>" 
                                                               class="btn btn-outline-primary btn-sm" title="إدارة التقييم">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
