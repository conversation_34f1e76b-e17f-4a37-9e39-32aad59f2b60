<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('student');

$user_id = $_SESSION['user_id'];
$error = '';
$success = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_profile'])) {
    $name = sanitize_input($_POST['name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone']);
    $address = sanitize_input($_POST['address']);
    $date_of_birth = $_POST['date_of_birth'];
    $gender = $_POST['gender'];
    $bio = sanitize_input($_POST['bio']);
    
    if (empty($name) || empty($email)) {
        $error = 'الاسم والبريد الإلكتروني مطلوبان';
    } elseif (!isValidEmail($email)) {
        $error = 'يرجى إدخال بريد إلكتروني صحيح';
    } else {
        try {
            // Check if email is already used by another user
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $user_id]);
            if ($stmt->fetch()) {
                $error = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
            } else {
                $stmt = $conn->prepare("
                    UPDATE users 
                    SET name = ?, email = ?, phone = ?, address = ?, date_of_birth = ?, gender = ?, bio = ?
                    WHERE id = ?
                ");
                $stmt->execute([$name, $email, $phone, $address, $date_of_birth, $gender, $bio, $user_id]);
                
                // Update session data
                $_SESSION['user_name'] = $name;
                $_SESSION['user_email'] = $email;
                
                logActivity($user_id, 'profile_update', 'تحديث الملف الشخصي');
                $success = 'تم تحديث الملف الشخصي بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ في تحديث الملف الشخصي';
        }
    }
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error = 'يرجى ملء جميع حقول كلمة المرور';
    } elseif (strlen($new_password) < 8) {
        $error = 'كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل';
    } elseif ($new_password !== $confirm_password) {
        $error = 'كلمة المرور الجديدة وتأكيدها غير متطابقتان';
    } else {
        try {
            // Verify current password
            $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch();
            
            if (!password_verify($current_password, $user['password'])) {
                $error = 'كلمة المرور الحالية غير صحيحة';
            } else {
                $hashed_password = hashPassword($new_password);
                $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                $stmt->execute([$hashed_password, $user_id]);
                
                logActivity($user_id, 'password_change', 'تغيير كلمة المرور');
                sendNotification($user_id, 'تم تغيير كلمة المرور', 'تم تغيير كلمة المرور الخاصة بك بنجاح', 'success');
                $success = 'تم تغيير كلمة المرور بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ في تغيير كلمة المرور';
        }
    }
}

// Handle profile image upload
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['upload_image'])) {
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
        $upload_result = uploadFile($_FILES['profile_image'], '../uploads/avatars/', $allowed_types, 5 * 1024 * 1024);
        
        if ($upload_result['success']) {
            try {
                // Delete old image if exists
                $stmt = $conn->prepare("SELECT profile_image FROM users WHERE id = ?");
                $stmt->execute([$user_id]);
                $old_image = $stmt->fetchColumn();
                if ($old_image && file_exists('../' . $old_image)) {
                    unlink('../' . $old_image);
                }
                
                // Update profile image
                $image_path = 'uploads/avatars/' . $upload_result['file_name'];
                $stmt = $conn->prepare("UPDATE users SET profile_image = ? WHERE id = ?");
                $stmt->execute([$image_path, $user_id]);
                
                logActivity($user_id, 'profile_image_update', 'تحديث صورة الملف الشخصي');
                $success = 'تم تحديث صورة الملف الشخصي بنجاح';
            } catch (PDOException $e) {
                $error = 'حدث خطأ في حفظ الصورة';
            }
        } else {
            $error = $upload_result['error'];
        }
    } else {
        $error = 'يرجى اختيار صورة صحيحة';
    }
}

// Get user data
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// Get user statistics
$stats_stmt = $conn->prepare("
    SELECT 
        COUNT(CASE WHEN e.status = 'active' THEN 1 END) as active_courses,
        COUNT(CASE WHEN e.status = 'completed' THEN 1 END) as completed_courses,
        SUM(c.duration_hours) as total_hours,
        AVG(ev.score) as avg_score
    FROM enrollments e
    LEFT JOIN courses c ON e.course_id = c.id
    LEFT JOIN evaluations ev ON e.user_id = ev.user_id AND e.course_id = ev.course_id
    WHERE e.user_id = ?
");
$stats_stmt->execute([$user_id]);
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

$page_title = 'الملف الشخصي';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                دوراتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="schedule.php">
                                <i class="fas fa-calendar-alt"></i>
                                جدولي الدراسي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="progress.php">
                                <i class="fas fa-chart-line"></i>
                                تقدمي الدراسي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="profile.php">
                                <i class="fas fa-user"></i>
                                الملف الشخصي
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">الملف الشخصي</h1>
                </div>

                <?php if ($error): ?>
                    <?php echo showAlert($error, 'danger'); ?>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <?php echo showAlert($success, 'success'); ?>
                <?php endif; ?>

                <div class="row">
                    <!-- Profile Overview -->
                    <div class="col-lg-4 mb-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center p-4">
                                <div class="profile-image-container mb-3">
                                    <?php if (isset($user['profile_image']) && $user['profile_image'] && file_exists('../' . $user['profile_image'])): ?>
                                        <img src="../<?php echo htmlspecialchars($user['profile_image']); ?>" 
                                             alt="صورة الملف الشخصي" 
                                             class="rounded-circle" 
                                             style="width: 120px; height: 120px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                                             style="width: 120px; height: 120px;">
                                            <i class="fas fa-user fa-3x"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <h5 class="fw-bold mb-1"><?php echo htmlspecialchars($user['name']); ?></h5>
                                <p class="text-muted mb-3"><?php echo htmlspecialchars($user['email']); ?></p>
                                
                                <form method="POST" enctype="multipart/form-data" class="mb-3">
                                    <div class="mb-2">
                                        <input type="file" class="form-control form-control-sm" 
                                               name="profile_image" accept="image/*" required>
                                    </div>
                                    <button type="submit" name="upload_image" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-upload me-1"></i>تحديث الصورة
                                    </button>
                                </form>
                                
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h6 class="fw-bold text-primary"><?php echo $stats['active_courses'] ?? 0; ?></h6>
                                        <small class="text-muted">دورات نشطة</small>
                                    </div>
                                    <div class="col-6">
                                        <h6 class="fw-bold text-success"><?php echo $stats['completed_courses'] ?? 0; ?></h6>
                                        <small class="text-muted">دورات مكتملة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Quick Stats -->
                        <div class="card border-0 shadow-sm mt-4">
                            <div class="card-header">
                                <h6 class="mb-0">إحصائيات سريعة</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>إجمالي ساعات التدريب</span>
                                    <strong><?php echo $stats['total_hours'] ?? 0; ?> ساعة</strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المعدل العام</span>
                                    <strong><?php echo $stats['avg_score'] ? number_format($stats['avg_score'], 1) : '0'; ?>%</strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>تاريخ التسجيل</span>
                                    <strong><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Profile Forms -->
                    <div class="col-lg-8">
                        <!-- Personal Information -->
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-user me-2"></i>
                                    المعلومات الشخصية
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="needs-validation" novalidate>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label">الاسم الكامل *</label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo htmlspecialchars($user['name']); ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                                   value="<?php echo $user['date_of_birth']; ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="gender" class="form-label">الجنس</label>
                                            <select class="form-select" id="gender" name="gender">
                                                <option value="">اختر الجنس</option>
                                                <option value="male" <?php echo $user['gender'] === 'male' ? 'selected' : ''; ?>>ذكر</option>
                                                <option value="female" <?php echo $user['gender'] === 'female' ? 'selected' : ''; ?>>أنثى</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="address" class="form-label">العنوان</label>
                                            <input type="text" class="form-control" id="address" name="address" 
                                                   value="<?php echo htmlspecialchars($user['address'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="bio" class="form-label">نبذة شخصية</label>
                                        <textarea class="form-control" id="bio" name="bio" rows="3" 
                                                  placeholder="اكتب نبذة مختصرة عن نفسك..."><?php echo htmlspecialchars($user['bio'] ?? ''); ?></textarea>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" name="update_profile" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Change Password -->
                        <div class="card border-0 shadow-sm">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-lock me-2"></i>
                                    تغيير كلمة المرور
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="needs-validation" novalidate>
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label">كلمة المرور الحالية *</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="current_password" 
                                                   name="current_password" required>
                                            <button type="button" class="btn btn-outline-secondary password-toggle" 
                                                    data-target="#current_password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="new_password" class="form-label">كلمة المرور الجديدة *</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="new_password" 
                                                   name="new_password" minlength="8" required>
                                            <button type="button" class="btn btn-outline-secondary password-toggle" 
                                                    data-target="#new_password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <div class="form-text">كلمة المرور يجب أن تكون 8 أحرف على الأقل</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="confirm_password" 
                                                   name="confirm_password" minlength="8" required>
                                            <button type="button" class="btn btn-outline-secondary password-toggle" 
                                                    data-target="#confirm_password">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" name="change_password" class="btn btn-warning">
                                            <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password')?.addEventListener('input', function() {
    const password = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// Profile image preview
document.querySelector('input[name="profile_image"]')?.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = document.querySelector('.profile-image-container img, .profile-image-container div');
            if (img.tagName === 'IMG') {
                img.src = e.target.result;
            }
        };
        reader.readAsDataURL(file);
    }
});
</script>

<?php include '../includes/footer.php'; ?>
