<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('admin');

// Get comprehensive statistics
$stats_stmt = $conn->prepare("
    SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'student') as total_students,
        (SELECT COUNT(*) FROM users WHERE role = 'instructor') as total_instructors,
        (SELECT COUNT(*) FROM courses WHERE status = 'active') as active_courses,
        (SELECT COUNT(*) FROM enrollments WHERE status = 'active') as active_enrollments,
        (SELECT COUNT(*) FROM course_sessions WHERE session_date >= CURDATE()) as upcoming_sessions,
        (SELECT COUNT(*) FROM course_sessions WHERE session_date < CURDATE()) as completed_sessions,
        (SELECT COUNT(DISTINCT session_id) FROM attendance WHERE status = 'present') as sessions_with_attendance,
        (SELECT COALESCE(AVG(
            CASE WHEN total_enrolled > 0
            THEN (present_count / total_enrolled) * 100
            ELSE 0 END
        ), 0) FROM (
            SELECT cs.id,
                   COUNT(DISTINCT e.user_id) as total_enrolled,
                   COUNT(DISTINCT CASE WHEN a.status = 'present' THEN a.user_id END) as present_count
            FROM course_sessions cs
            JOIN courses c ON cs.course_id = c.id
            LEFT JOIN enrollments e ON c.id = e.course_id AND e.status IN ('active', 'completed')
            LEFT JOIN attendance a ON cs.id = a.session_id
            WHERE cs.session_date < CURDATE()
            GROUP BY cs.id
        ) as attendance_stats) as avg_attendance_rate
");
$stats_stmt->execute();
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// Get monthly enrollment trends
$monthly_enrollments_stmt = $conn->prepare("
    SELECT
        DATE_FORMAT(enrolled_at, '%Y-%m') as month,
        COUNT(*) as enrollments
    FROM enrollments
    WHERE enrolled_at >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(enrolled_at, '%Y-%m')
    ORDER BY month
");
$monthly_enrollments_stmt->execute();
$monthly_enrollments = $monthly_enrollments_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get top courses by enrollment
$top_courses_stmt = $conn->prepare("
    SELECT c.title, c.id, COUNT(e.id) as enrollment_count,
           AVG(CASE WHEN ev.score IS NOT NULL AND ev.max_score > 0
               THEN (ev.score / ev.max_score) * 100 ELSE NULL END) as avg_score
    FROM courses c
    LEFT JOIN enrollments e ON c.id = e.course_id AND e.status IN ('active', 'completed')
    LEFT JOIN evaluations ev ON c.id = ev.course_id AND ev.score IS NOT NULL
    WHERE c.status = 'active'
    GROUP BY c.id, c.title
    ORDER BY enrollment_count DESC
    LIMIT 10
");
$top_courses_stmt->execute();
$top_courses = $top_courses_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get instructor performance
$instructor_performance_stmt = $conn->prepare("
    SELECT u.name, u.id,
           COUNT(DISTINCT c.id) as courses_count,
           COUNT(DISTINCT e.user_id) as total_students,
           AVG(CASE WHEN ev.score IS NOT NULL AND ev.max_score > 0
               THEN (ev.score / ev.max_score) * 100 ELSE NULL END) as avg_student_score
    FROM users u
    LEFT JOIN courses c ON u.id = c.instructor_id AND c.status = 'active'
    LEFT JOIN enrollments e ON c.id = e.course_id AND e.status IN ('active', 'completed')
    LEFT JOIN evaluations ev ON c.id = ev.course_id AND ev.score IS NOT NULL
    WHERE u.role = 'instructor'
    GROUP BY u.id, u.name
    HAVING courses_count > 0
    ORDER BY total_students DESC
    LIMIT 10
");
$instructor_performance_stmt->execute();
$instructor_performance = $instructor_performance_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'التقارير والإحصائيات';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                إدارة الدورات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="enrollments.php">
                                <i class="fas fa-user-graduate"></i>
                                التسجيلات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="categories.php">
                                <i class="fas fa-tags"></i>
                                التصنيفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="calendar.php">
                                <i class="fas fa-calendar"></i>
                                التقويم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="reports.php">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">التقارير والإحصائيات</h1>
                </div>

                <!-- Quick Stats -->
                <div class="row g-4 mb-5">
                    <div class="col-xl-3 col-md-6">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <h3><?php echo getTotalStudents(); ?></h3>
                                <p>إجمالي الطلاب</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="card stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                            <div class="card-body text-center">
                                <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                                <h3><?php echo getTotalCourses(); ?></h3>
                                <p>إجمالي الدورات</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="card stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                            <div class="card-body text-center">
                                <i class="fas fa-user-check fa-3x mb-3"></i>
                                <h3><?php echo getActiveStudents(); ?></h3>
                                <p>الطلاب النشطون</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="card stats-card" style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);">
                            <div class="card-body text-center">
                                <i class="fas fa-chalkboard-teacher fa-3x mb-3"></i>
                                <h3><?php echo getTotalInstructors(); ?></h3>
                                <p>المدربون</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Section -->
                <div class="row g-4">
                    <!-- Enrollment Reports -->
                    <div class="col-lg-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    تقارير التسجيل
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="#" class="btn btn-outline-primary text-start" onclick="generateReport('enrollments')">
                                        <i class="fas fa-download me-2"></i>
                                        تقرير التسجيلات الشهري
                                    </a>
                                    <a href="#" class="btn btn-outline-primary text-start" onclick="generateReport('course_popularity')">
                                        <i class="fas fa-star me-2"></i>
                                        تقرير شعبية الدورات
                                    </a>
                                    <a href="#" class="btn btn-outline-primary text-start" onclick="generateReport('enrollment_trends')">
                                        <i class="fas fa-trending-up me-2"></i>
                                        اتجاهات التسجيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Student Reports -->
                    <div class="col-lg-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user-graduate me-2"></i>
                                    تقارير الطلاب
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="#" class="btn btn-outline-success text-start" onclick="generateReport('student_progress')">
                                        <i class="fas fa-chart-pie me-2"></i>
                                        تقرير تقدم الطلاب
                                    </a>
                                    <a href="#" class="btn btn-outline-success text-start" onclick="generateReport('completion_rates')">
                                        <i class="fas fa-percentage me-2"></i>
                                        معدلات الإنجاز
                                    </a>
                                    <a href="#" class="btn btn-outline-success text-start" onclick="generateReport('attendance')">
                                        <i class="fas fa-calendar-check me-2"></i>
                                        تقرير الحضور
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Reports -->
                    <div class="col-lg-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-dollar-sign me-2"></i>
                                    التقارير المالية
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="#" class="btn btn-outline-warning text-start" onclick="generateReport('revenue')">
                                        <i class="fas fa-money-bill-wave me-2"></i>
                                        تقرير الإيرادات
                                    </a>
                                    <a href="#" class="btn btn-outline-warning text-start" onclick="generateReport('payments')">
                                        <i class="fas fa-credit-card me-2"></i>
                                        تقرير المدفوعات
                                    </a>
                                    <a href="#" class="btn btn-outline-warning text-start" onclick="generateReport('refunds')">
                                        <i class="fas fa-undo me-2"></i>
                                        تقرير المبالغ المستردة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Course Reports -->
                    <div class="col-lg-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-book me-2"></i>
                                    تقارير الدورات
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="#" class="btn btn-outline-info text-start" onclick="generateReport('course_performance')">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        أداء الدورات
                                    </a>
                                    <a href="#" class="btn btn-outline-info text-start" onclick="generateReport('instructor_performance')">
                                        <i class="fas fa-chalkboard-teacher me-2"></i>
                                        أداء المدربين
                                    </a>
                                    <a href="#" class="btn btn-outline-info text-start" onclick="generateReport('course_ratings')">
                                        <i class="fas fa-star me-2"></i>
                                        تقييمات الدورات
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Analytics -->
                <div class="row mt-5">
                    <div class="col">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-analytics me-2"></i>
                                    تحليلات سريعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <canvas id="enrollmentChart" height="200"></canvas>
                                    </div>
                                    <div class="col-md-6">
                                        <canvas id="categoryChart" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Report Generation Modal -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تجهيز التقرير</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p>جاري تجهيز التقرير...</p>
                <p class="text-muted">سيتم تنزيل التقرير تلقائياً عند اكتماله</p>
            </div>
        </div>
    </div>
</div>

<script>
// Real data for charts
const enrollmentData = {
    labels: <?php echo json_encode(array_map(function($item) {
        $date = new Date($item['month'] . '-01');
        return $date->format('F Y');
    }, $monthly_enrollments)); ?>,
    datasets: [{
        label: 'التسجيلات',
        data: <?php echo json_encode(array_column($monthly_enrollments, 'enrollments')); ?>,
        borderColor: 'rgb(13, 110, 253)',
        backgroundColor: 'rgba(13, 110, 253, 0.1)',
        tension: 0.4
    }]
};

const categoryData = {
    labels: ['البرمجة', 'قواعد البيانات', 'الأمن السيبراني', 'الذكاء الاصطناعي', 'إدارة المشاريع'],
    datasets: [{
        data: [30, 20, 25, 15, 10],
        backgroundColor: [
            '#0d6efd',
            '#28a745',
            '#dc3545',
            '#6f42c1',
            '#fd7e14'
        ]
    }]
};

// Initialize charts
const enrollmentChart = new Chart(document.getElementById('enrollmentChart'), {
    type: 'line',
    data: enrollmentData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: 'التسجيلات الشهرية'
            }
        }
    }
});

const categoryChart = new Chart(document.getElementById('categoryChart'), {
    type: 'doughnut',
    data: categoryData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: 'توزيع الدورات حسب التصنيف'
            }
        }
    }
});

function generateReport(reportType) {
    // Show loading modal
    new bootstrap.Modal(document.getElementById('reportModal')).show();
    
    // Simulate report generation
    setTimeout(function() {
        // Hide modal
        bootstrap.Modal.getInstance(document.getElementById('reportModal')).hide();
        
        // Show success message
        TrainingPlatform.showAlert('تم تجهيز التقرير بنجاح وسيتم تنزيله قريباً', 'success');
        
        // In a real application, you would trigger a file download here
        console.log('Generating report:', reportType);
    }, 3000);
}
</script>

<?php include '../includes/footer.php'; ?>