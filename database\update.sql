-- Database Update Script for Training Platform
-- This script adds missing tables and columns to existing database

-- Add missing columns to existing tables if they don't exist

-- Add reset token fields to users table
ALTER TABLE users
ADD COLUMN IF NOT EXISTS reset_token VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS reset_expires TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS profile_image VARCHAR(500) NULL;

-- Add completion date to enrollments table
ALTER TABLE enrollments 
ADD COLUMN IF NOT EXISTS completion_date TIMESTAMP NULL;

-- Add additional fields to courses table
ALTER TABLE courses
ADD COLUMN IF NOT EXISTS objectives TEXT NULL,
ADD COLUMN IF NOT EXISTS prerequisites TEXT NULL,
ADD COLUMN IF NOT EXISTS syllabus TEXT NULL,
ADD COLUMN IF NOT EXISTS image VARCHAR(500) NULL;

-- Add bio and specialization to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS bio TEXT NULL,
ADD COLUMN IF NOT EXISTS specialization VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS phone VARCHAR(20) NULL,
ADD COLUMN IF NOT EXISTS address TEXT NULL,
ADD COLUMN IF NOT EXISTS date_of_birth DATE NULL,
ADD COLUMN IF NOT EXISTS gender ENUM('male', 'female') NULL,
ADD COLUMN IF NOT EXISTS profile_image VARCHAR(500) NULL;

-- Create course_materials table if not exists
CREATE TABLE IF NOT EXISTS course_materials (
    id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_path VARCHAR(500) NOT NULL,
    file_type ENUM('pdf', 'video', 'image', 'document') DEFAULT 'document',
    file_size BIGINT DEFAULT 0,
    sort_order INT DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);

-- Create evaluations table if not exists
CREATE TABLE IF NOT EXISTS evaluations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    instructor_id INT NOT NULL,
    evaluation_type ENUM('quiz', 'assignment', 'project', 'final', 'participation') NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    max_score DECIMAL(5,2) NOT NULL DEFAULT 100.00,
    score DECIMAL(5,2) NULL,
    grade VARCHAR(10) NULL,
    feedback TEXT,
    evaluation_date DATE,
    graded_at TIMESTAMP NULL,
    status ENUM('pending', 'submitted', 'graded', 'reviewed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create course_reviews table if not exists
CREATE TABLE IF NOT EXISTS course_reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_course_review (user_id, course_id)
);

-- Create attendance table if not exists
CREATE TABLE IF NOT EXISTS attendance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    user_id INT NOT NULL,
    status ENUM('present', 'absent', 'late', 'excused') DEFAULT 'absent',
    check_in_time TIMESTAMP NULL,
    check_out_time TIMESTAMP NULL,
    notes TEXT,
    recorded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES course_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_session_user (session_id, user_id)
);

-- Create payments table if not exists
CREATE TABLE IF NOT EXISTS payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'SAR',
    payment_method ENUM('credit_card', 'bank_transfer', 'cash', 'online') NOT NULL,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(255),
    payment_gateway VARCHAR(100),
    payment_date TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (enrollment_id) REFERENCES enrollments(id) ON DELETE CASCADE
);

-- Create certificates table if not exists
CREATE TABLE IF NOT EXISTS certificates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_id INT NOT NULL,
    certificate_number VARCHAR(50) UNIQUE NOT NULL,
    issue_date DATE NOT NULL,
    expiry_date DATE NULL,
    certificate_data JSON,
    file_path VARCHAR(500),
    status ENUM('active', 'revoked', 'expired') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (enrollment_id) REFERENCES enrollments(id) ON DELETE CASCADE
);

-- Create messages table if not exists
CREATE TABLE IF NOT EXISTS messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sender_id INT NOT NULL,
    recipient_id INT NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    parent_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES messages(id) ON DELETE CASCADE
);

-- Create system_logs table if not exists
CREATE TABLE IF NOT EXISTS system_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level ENUM('info', 'warning', 'error', 'critical') NOT NULL,
    message TEXT NOT NULL,
    context JSON,
    user_id INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create file_uploads table if not exists
CREATE TABLE IF NOT EXISTS file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_type ENUM('image', 'document', 'video', 'audio', 'other') NOT NULL,
    upload_context VARCHAR(100),
    context_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Add indexes for better performance (only if they don't exist)
CREATE INDEX IF NOT EXISTS idx_course_materials_course ON course_materials(course_id);
CREATE INDEX IF NOT EXISTS idx_evaluations_user ON evaluations(user_id);
CREATE INDEX IF NOT EXISTS idx_evaluations_course ON evaluations(course_id);
CREATE INDEX IF NOT EXISTS idx_evaluations_instructor ON evaluations(instructor_id);
CREATE INDEX IF NOT EXISTS idx_course_reviews_course ON course_reviews(course_id);
CREATE INDEX IF NOT EXISTS idx_attendance_session ON attendance(session_id);
CREATE INDEX IF NOT EXISTS idx_attendance_user ON attendance(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_user ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_certificates_user ON certificates(user_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_recipient ON messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_file_uploads_user ON file_uploads(user_id);
CREATE INDEX IF NOT EXISTS idx_file_uploads_context ON file_uploads(upload_context, context_id);

-- Insert additional sample data if needed

-- Insert sample categories if they don't exist
INSERT IGNORE INTO categories (name, description, color) VALUES
('البرمجة وتطوير الويب', 'دورات في البرمجة وتطوير المواقع والتطبيقات', '#007bff'),
('قواعد البيانات', 'دورات في إدارة وتصميم قواعد البيانات', '#28a745'),
('الشبكات والأمن السيبراني', 'دورات في أمن المعلومات والشبكات', '#dc3545'),
('الذكاء الاصطناعي', 'دورات في الذكاء الاصطناعي وتعلم الآلة', '#6f42c1'),
('تصميم الجرافيك', 'دورات في التصميم والجرافيك', '#fd7e14'),
('إدارة المشاريع التقنية', 'دورات في إدارة المشاريع التقنية', '#20c997');

-- Insert additional settings if they don't exist
INSERT IGNORE INTO settings (key_name, value, description, type) VALUES
('platform_version', '1.0.0', 'إصدار المنصة', 'text'),
('maintenance_mode', 'false', 'وضع الصيانة', 'boolean'),
('registration_enabled', 'true', 'تفعيل التسجيل الجديد', 'boolean'),
('max_file_upload_size', '50', 'الحد الأقصى لحجم الملف بالميجابايت', 'number'),
('allowed_file_types', 'pdf,doc,docx,ppt,pptx,xls,xlsx,jpg,jpeg,png,gif,mp4,avi,mov', 'أنواع الملفات المسموحة', 'text'),
('session_timeout', '3600', 'مهلة انتهاء الجلسة بالثواني', 'number'),
('backup_frequency', 'daily', 'تكرار النسخ الاحتياطي', 'text'),
('email_smtp_host', '', 'خادم SMTP للبريد الإلكتروني', 'text'),
('email_smtp_port', '587', 'منفذ SMTP', 'number'),
('email_smtp_username', '', 'اسم مستخدم SMTP', 'text'),
('email_smtp_password', '', 'كلمة مرور SMTP', 'password'),
('social_facebook', '', 'رابط صفحة فيسبوك', 'text'),
('social_twitter', '', 'رابط حساب تويتر', 'text'),
('social_linkedin', '', 'رابط صفحة لينكد إن', 'text'),
('social_instagram', '', 'رابط حساب إنستغرام', 'text'),
('google_analytics_id', '', 'معرف Google Analytics', 'text'),
('recaptcha_site_key', '', 'مفتاح موقع reCAPTCHA', 'text'),
('recaptcha_secret_key', '', 'المفتاح السري لـ reCAPTCHA', 'password');

-- Update existing data if needed
UPDATE settings SET value = 'منصة التدريب المهني المطورة' WHERE key_name = 'site_name' AND value = 'منصة التدريب المهني';

-- Add completion date for existing completed enrollments
UPDATE enrollments 
SET completion_date = updated_at 
WHERE status = 'completed' AND completion_date IS NULL;

-- Create a trigger to automatically set completion_date when status changes to completed
DELIMITER //
CREATE TRIGGER IF NOT EXISTS set_completion_date 
    BEFORE UPDATE ON enrollments
    FOR EACH ROW
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        SET NEW.completion_date = NOW();
    END IF;
END//
DELIMITER ;

-- Create a trigger to generate certificate number
DELIMITER //
CREATE TRIGGER IF NOT EXISTS generate_certificate_number
    BEFORE INSERT ON certificates
    FOR EACH ROW
BEGIN
    IF NEW.certificate_number IS NULL OR NEW.certificate_number = '' THEN
        SET NEW.certificate_number = CONCAT('CERT-', LPAD(NEW.id, 6, '0'));
    END IF;
END//
DELIMITER ;
