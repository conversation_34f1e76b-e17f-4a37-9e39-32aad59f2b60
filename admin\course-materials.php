<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('admin');

$course_id = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;

if (!$course_id) {
    redirect('courses.php');
}

// Get course details
$course_stmt = $conn->prepare("SELECT * FROM courses WHERE id = ?");
$course_stmt->execute([$course_id]);
$course = $course_stmt->fetch(PDO::FETCH_ASSOC);

if (!$course) {
    redirect('courses.php');
}

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['upload_material'])) {
    $title = sanitize_input($_POST['title']);
    $description = sanitize_input($_POST['description']);
    $is_public = isset($_POST['is_public']) ? 1 : 0;
    $sort_order = intval($_POST['sort_order']);
    
    if (empty($title)) {
        $_SESSION['flash_message'] = 'يرجى إدخال عنوان المادة';
        $_SESSION['flash_type'] = 'danger';
    } elseif (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        $_SESSION['flash_message'] = 'يرجى اختيار ملف للرفع';
        $_SESSION['flash_type'] = 'danger';
    } else {
        $file = $_FILES['file'];
        $allowed_types = ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt', 'mp4', 'avi', 'mov', 'jpg', 'jpeg', 'png', 'gif'];
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($file_extension, $allowed_types)) {
            $_SESSION['flash_message'] = 'نوع الملف غير مدعوم';
            $_SESSION['flash_type'] = 'danger';
        } elseif ($file['size'] > 50 * 1024 * 1024) { // 50MB limit
            $_SESSION['flash_message'] = 'حجم الملف كبير جداً (الحد الأقصى 50 ميجابايت)';
            $_SESSION['flash_type'] = 'danger';
        } else {
            $upload_dir = '../uploads/materials/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $file_name = uniqid() . '.' . $file_extension;
            $file_path = $upload_dir . $file_name;
            
            if (move_uploaded_file($file['tmp_name'], $file_path)) {
                // Determine file type
                $file_type = 'document';
                if (in_array($file_extension, ['mp4', 'avi', 'mov'])) {
                    $file_type = 'video';
                } elseif (in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                    $file_type = 'image';
                } elseif ($file_extension === 'pdf') {
                    $file_type = 'pdf';
                }
                
                try {
                    $stmt = $conn->prepare("
                        INSERT INTO course_materials (course_id, title, description, file_path, file_type, file_size, sort_order, is_public) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $course_id, 
                        $title, 
                        $description, 
                        'uploads/materials/' . $file_name, 
                        $file_type, 
                        $file['size'], 
                        $sort_order, 
                        $is_public
                    ]);
                    
                    $_SESSION['flash_message'] = 'تم رفع المادة بنجاح';
                    $_SESSION['flash_type'] = 'success';
                } catch (PDOException $e) {
                    unlink($file_path); // Delete uploaded file
                    $_SESSION['flash_message'] = 'حدث خطأ في حفظ المادة';
                    $_SESSION['flash_type'] = 'danger';
                }
            } else {
                $_SESSION['flash_message'] = 'فشل في رفع الملف';
                $_SESSION['flash_type'] = 'danger';
            }
        }
    }
    
    redirect("course-materials.php?course_id=$course_id");
}

// Handle material deletion
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_material'])) {
    $material_id = intval($_POST['material_id']);
    
    try {
        // Get file path before deletion
        $stmt = $conn->prepare("SELECT file_path FROM course_materials WHERE id = ? AND course_id = ?");
        $stmt->execute([$material_id, $course_id]);
        $material = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($material) {
            // Delete from database
            $stmt = $conn->prepare("DELETE FROM course_materials WHERE id = ? AND course_id = ?");
            $stmt->execute([$material_id, $course_id]);
            
            // Delete file
            $file_path = '../' . $material['file_path'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
            
            $_SESSION['flash_message'] = 'تم حذف المادة بنجاح';
            $_SESSION['flash_type'] = 'success';
        }
    } catch (PDOException $e) {
        $_SESSION['flash_message'] = 'حدث خطأ في حذف المادة';
        $_SESSION['flash_type'] = 'danger';
    }
    
    redirect("course-materials.php?course_id=$course_id");
}

// Get course materials
$materials_stmt = $conn->prepare("
    SELECT * FROM course_materials 
    WHERE course_id = ? 
    ORDER BY sort_order, title
");
$materials_stmt->execute([$course_id]);
$materials = $materials_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'إدارة مواد الدورة - ' . $course['title'];
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                إدارة الدورات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="#">
                                <i class="fas fa-file-alt"></i>
                                مواد الدورة
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 fw-bold">مواد الدورة</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">الرئيسية</a></li>
                                <li class="breadcrumb-item"><a href="courses.php">الدورات</a></li>
                                <li class="breadcrumb-item active"><?php echo htmlspecialchars($course['title']); ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="fas fa-plus me-2"></i>إضافة مادة
                        </button>
                    </div>
                </div>

                <!-- Course Info -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo htmlspecialchars($course['title']); ?></h5>
                        <p class="card-text text-muted"><?php echo htmlspecialchars($course['short_description']); ?></p>
                    </div>
                </div>

                <!-- Materials List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            المواد التعليمية (<?php echo count($materials); ?>)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($materials)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <h5>لا توجد مواد تعليمية</h5>
                                <p class="text-muted">ابدأ بإضافة المواد التعليمية للدورة</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                                    <i class="fas fa-plus me-2"></i>إضافة أول مادة
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>العنوان</th>
                                            <th>النوع</th>
                                            <th>الحجم</th>
                                            <th>الترتيب</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإضافة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($materials as $material): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-file-<?php echo $material['file_type']; ?> me-2 text-primary"></i>
                                                        <div>
                                                            <h6 class="mb-0"><?php echo htmlspecialchars($material['title']); ?></h6>
                                                            <?php if ($material['description']): ?>
                                                                <small class="text-muted"><?php echo htmlspecialchars($material['description']); ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php
                                                        $types = [
                                                            'pdf' => 'PDF',
                                                            'video' => 'فيديو',
                                                            'image' => 'صورة',
                                                            'document' => 'مستند'
                                                        ];
                                                        echo $types[$material['file_type']] ?? $material['file_type'];
                                                        ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatFileSize($material['file_size']); ?></td>
                                                <td><?php echo $material['sort_order']; ?></td>
                                                <td>
                                                    <?php if ($material['is_public']): ?>
                                                        <span class="badge bg-success">عام</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">خاص</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('d/m/Y', strtotime($material['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="../<?php echo $material['file_path']; ?>" 
                                                           class="btn btn-outline-primary" target="_blank" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="../<?php echo $material['file_path']; ?>" 
                                                           class="btn btn-outline-success" download title="تحميل">
                                                            <i class="fas fa-download"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-outline-danger" 
                                                                onclick="deleteMaterial(<?php echo $material['id']; ?>, '<?php echo htmlspecialchars($material['title']); ?>')" 
                                                                title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مادة تعليمية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان المادة *</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="file" class="form-label">الملف *</label>
                        <input type="file" class="form-control" id="file" name="file" required>
                        <div class="form-text">
                            الأنواع المدعومة: PDF, DOC, PPT, XLS, TXT, MP4, AVI, JPG, PNG<br>
                            الحد الأقصى: 50 ميجابايت
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">ترتيب العرض</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" value="0" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_public" name="is_public">
                                    <label class="form-check-label" for="is_public">
                                        متاح للعموم
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="upload_material" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>رفع المادة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Form -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="delete_material" value="1">
    <input type="hidden" name="material_id" id="deleteMaterialId">
</form>

<script>
function deleteMaterial(id, title) {
    if (confirm(`هل أنت متأكد من حذف المادة "${title}"؟\n\nسيتم حذف الملف نهائياً ولا يمكن استرداده.`)) {
        document.getElementById('deleteMaterialId').value = id;
        document.getElementById('deleteForm').submit();
    }
}

// File size validation
document.getElementById('file').addEventListener('change', function() {
    const file = this.files[0];
    if (file && file.size > 50 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. الحد الأقصى 50 ميجابايت.');
        this.value = '';
    }
});
</script>

<?php
// Helper function to format file size
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' جيجابايت';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' ميجابايت';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' كيلوبايت';
    } else {
        return $bytes . ' بايت';
    }
}

include '../includes/footer.php';
?>
