<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('student');

$user_id = $_SESSION['user_id'];
$course_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$course_id) {
    header('Location: courses.php');
    exit;
}

// Check if student is enrolled in this course
$enrollment_stmt = $conn->prepare("
    SELECT e.*, c.title, c.description, c.objectives, c.prerequisites, c.syllabus,
           c.duration_hours, c.price, c.instructor_id, c.image,
           u.name as instructor_name, u.bio as instructor_bio,
           cat.name as category_name
    FROM enrollments e
    JOIN courses c ON e.course_id = c.id
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN categories cat ON c.category_id = cat.id
    WHERE e.user_id = ? AND e.course_id = ? AND e.status IN ('active', 'completed')
");
$enrollment_stmt->execute([$user_id, $course_id]);
$enrollment = $enrollment_stmt->fetch(PDO::FETCH_ASSOC);

if (!$enrollment) {
    header('Location: browse-courses.php');
    exit;
}

// Get course materials
$materials_stmt = $conn->prepare("
    SELECT * FROM course_materials 
    WHERE course_id = ? AND (is_public = 1 OR is_public IS NULL)
    ORDER BY sort_order ASC, created_at DESC
");
$materials_stmt->execute([$course_id]);
$materials = $materials_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get course sessions
$sessions_stmt = $conn->prepare("
    SELECT cs.*, a.status as attendance_status, a.check_in_time, a.check_out_time
    FROM course_sessions cs
    LEFT JOIN attendance a ON cs.id = a.session_id AND a.user_id = ?
    WHERE cs.course_id = ?
    ORDER BY cs.session_date ASC, cs.start_time ASC
");
$sessions_stmt->execute([$user_id, $course_id]);
$sessions = $sessions_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get student's evaluations for this course
$evaluations_stmt = $conn->prepare("
    SELECT * FROM evaluations 
    WHERE user_id = ? AND course_id = ?
    ORDER BY created_at DESC
");
$evaluations_stmt->execute([$user_id, $course_id]);
$evaluations = $evaluations_stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate progress
$total_sessions = count($sessions);
$attended_sessions = count(array_filter($sessions, function($s) { return $s['attendance_status'] === 'present'; }));
$attendance_rate = $total_sessions > 0 ? round(($attended_sessions / $total_sessions) * 100, 1) : 0;

$graded_evaluations = array_filter($evaluations, function($e) { return $e['score'] !== null; });
$avg_score = !empty($graded_evaluations) ? array_sum(array_column($graded_evaluations, 'score')) / count($graded_evaluations) : 0;

$page_title = 'مراجعة المحتوى - ' . $enrollment['title'];
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                دوراتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="schedule.php">
                                <i class="fas fa-calendar-alt"></i>
                                جدولي الدراسي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="progress.php">
                                <i class="fas fa-chart-line"></i>
                                تقدمي الدراسي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="certificates.php">
                                <i class="fas fa-certificate"></i>
                                شهاداتي
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 fw-bold">مراجعة المحتوى</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="courses.php">دوراتي</a></li>
                                <li class="breadcrumb-item active"><?php echo htmlspecialchars($enrollment['title']); ?></li>
                            </ol>
                        </nav>
                    </div>
                    <a href="courses.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>

                <!-- Course Header -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h2 class="fw-bold mb-3"><?php echo htmlspecialchars($enrollment['title']); ?></h2>
                                <p class="text-muted mb-3"><?php echo htmlspecialchars($enrollment['description']); ?></p>
                                
                                <div class="row">
                                    <div class="col-sm-6">
                                        <small class="text-muted">المدرب:</small>
                                        <p class="mb-2"><?php echo htmlspecialchars($enrollment['instructor_name']); ?></p>
                                    </div>
                                    <div class="col-sm-6">
                                        <small class="text-muted">التصنيف:</small>
                                        <p class="mb-2"><?php echo htmlspecialchars($enrollment['category_name'] ?? 'غير محدد'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h5 class="fw-bold text-primary"><?php echo $attendance_rate; ?>%</h5>
                                        <small class="text-muted">معدل الحضور</small>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="fw-bold text-success"><?php echo number_format($avg_score, 1); ?>%</h5>
                                        <small class="text-muted">المعدل العام</small>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <span class="badge bg-<?php 
                                        echo match($enrollment['status']) {
                                            'active' => 'primary',
                                            'completed' => 'success',
                                            'pending' => 'warning',
                                            default => 'secondary'
                                        };
                                    ?> fs-6">
                                        <?php 
                                        echo match($enrollment['status']) {
                                            'active' => 'نشط',
                                            'completed' => 'مكتمل',
                                            'pending' => 'معلق',
                                            default => $enrollment['status']
                                        };
                                        ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tabs -->
                <ul class="nav nav-tabs mb-4" id="courseContentTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials" type="button">
                            <i class="fas fa-file-alt me-2"></i>المواد التعليمية
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="sessions-tab" data-bs-toggle="tab" data-bs-target="#sessions" type="button">
                            <i class="fas fa-calendar-alt me-2"></i>الجلسات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="evaluations-tab" data-bs-toggle="tab" data-bs-target="#evaluations" type="button">
                            <i class="fas fa-clipboard-check me-2"></i>التقييمات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button">
                            <i class="fas fa-info-circle me-2"></i>تفاصيل الدورة
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="courseContentTabsContent">
                    <!-- Materials Tab -->
                    <div class="tab-pane fade show active" id="materials" role="tabpanel">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header">
                                <h5 class="mb-0">المواد التعليمية</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($materials)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                        <h6>لا توجد مواد متاحة</h6>
                                        <p class="text-muted">لم يتم رفع أي مواد تعليمية لهذه الدورة بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="row g-3">
                                        <?php foreach ($materials as $material): ?>
                                            <div class="col-md-6 col-lg-4">
                                                <div class="card border">
                                                    <div class="card-body">
                                                        <div class="d-flex align-items-start">
                                                            <div class="me-3">
                                                                <i class="fas fa-<?php 
                                                                    echo match($material['file_type']) {
                                                                        'pdf' => 'file-pdf text-danger',
                                                                        'video' => 'file-video text-primary',
                                                                        'image' => 'file-image text-success',
                                                                        default => 'file-alt text-secondary'
                                                                    };
                                                                ?> fa-2x"></i>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <h6 class="card-title"><?php echo htmlspecialchars($material['title']); ?></h6>
                                                                <?php if ($material['description']): ?>
                                                                    <p class="card-text small text-muted"><?php echo htmlspecialchars($material['description']); ?></p>
                                                                <?php endif; ?>
                                                                <small class="text-muted">
                                                                    <?php echo formatFileSize($material['file_size']); ?> • 
                                                                    <?php echo date('d/m/Y', strtotime($material['created_at'])); ?>
                                                                </small>
                                                            </div>
                                                        </div>
                                                        <div class="mt-3">
                                                            <a href="../<?php echo htmlspecialchars($material['file_path']); ?>" 
                                                               class="btn btn-primary btn-sm" target="_blank">
                                                                <i class="fas fa-download me-1"></i>تحميل
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Sessions Tab -->
                    <div class="tab-pane fade" id="sessions" role="tabpanel">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header">
                                <h5 class="mb-0">جلسات الدورة</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($sessions)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                        <h6>لا توجد جلسات</h6>
                                        <p class="text-muted">لم يتم جدولة أي جلسات لهذه الدورة بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>العنوان</th>
                                                    <th>التاريخ والوقت</th>
                                                    <th>المكان</th>
                                                    <th>حالة الحضور</th>
                                                    <th>وقت الدخول</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($sessions as $session): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($session['title']); ?></strong>
                                                            <?php if ($session['description']): ?>
                                                                <br><small class="text-muted"><?php echo htmlspecialchars($session['description']); ?></small>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <strong><?php echo date('d/m/Y', strtotime($session['session_date'])); ?></strong>
                                                            <br><small><?php echo date('H:i', strtotime($session['start_time'])); ?> - <?php echo date('H:i', strtotime($session['end_time'])); ?></small>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($session['location'] ?? 'غير محدد'); ?></td>
                                                        <td>
                                                            <?php if ($session['attendance_status']): ?>
                                                                <span class="badge bg-<?php 
                                                                    echo match($session['attendance_status']) {
                                                                        'present' => 'success',
                                                                        'late' => 'warning',
                                                                        'absent' => 'danger',
                                                                        'excused' => 'info',
                                                                        default => 'secondary'
                                                                    };
                                                                ?>">
                                                                    <?php 
                                                                    echo match($session['attendance_status']) {
                                                                        'present' => 'حاضر',
                                                                        'late' => 'متأخر',
                                                                        'absent' => 'غائب',
                                                                        'excused' => 'معذور',
                                                                        default => 'غير محدد'
                                                                    };
                                                                    ?>
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="badge bg-secondary">غير مسجل</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php echo $session['check_in_time'] ? date('H:i', strtotime($session['check_in_time'])) : '-'; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Evaluations Tab -->
                    <div class="tab-pane fade" id="evaluations" role="tabpanel">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header">
                                <h5 class="mb-0">التقييمات والدرجات</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($evaluations)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                                        <h6>لا توجد تقييمات</h6>
                                        <p class="text-muted">لم يتم إنشاء أي تقييمات لهذه الدورة بعد</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>التقييم</th>
                                                    <th>النوع</th>
                                                    <th>الدرجة</th>
                                                    <th>النسبة</th>
                                                    <th>التقدير</th>
                                                    <th>التاريخ</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($evaluations as $eval): ?>
                                                    <tr>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($eval['title']); ?></strong>
                                                            <?php if ($eval['description']): ?>
                                                                <br><small class="text-muted"><?php echo htmlspecialchars($eval['description']); ?></small>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-secondary">
                                                                <?php 
                                                                echo match($eval['evaluation_type']) {
                                                                    'quiz' => 'اختبار',
                                                                    'assignment' => 'واجب',
                                                                    'project' => 'مشروع',
                                                                    'final' => 'نهائي',
                                                                    'participation' => 'مشاركة',
                                                                    default => $eval['evaluation_type']
                                                                };
                                                                ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <?php if ($eval['score'] !== null): ?>
                                                                <strong><?php echo $eval['score']; ?></strong> / <?php echo $eval['max_score']; ?>
                                                            <?php else: ?>
                                                                <span class="text-muted">لم يتم التقييم</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($eval['score'] !== null): ?>
                                                                <?php $percentage = round(($eval['score'] / $eval['max_score']) * 100, 1); ?>
                                                                <span class="badge bg-<?php echo $percentage >= 80 ? 'success' : ($percentage >= 60 ? 'warning' : 'danger'); ?>">
                                                                    <?php echo $percentage; ?>%
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="text-muted">-</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($eval['grade']): ?>
                                                                <span class="badge bg-primary"><?php echo $eval['grade']; ?></span>
                                                            <?php else: ?>
                                                                <span class="text-muted">-</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($eval['graded_at']): ?>
                                                                <?php echo date('d/m/Y', strtotime($eval['graded_at'])); ?>
                                                            <?php else: ?>
                                                                <span class="text-muted">معلق</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Details Tab -->
                    <div class="tab-pane fade" id="details" role="tabpanel">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header">
                                <h5 class="mb-0">تفاصيل الدورة</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($enrollment['objectives']): ?>
                                    <div class="mb-4">
                                        <h6 class="fw-bold">أهداف الدورة:</h6>
                                        <p><?php echo nl2br(htmlspecialchars($enrollment['objectives'])); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($enrollment['prerequisites']): ?>
                                    <div class="mb-4">
                                        <h6 class="fw-bold">المتطلبات المسبقة:</h6>
                                        <p><?php echo nl2br(htmlspecialchars($enrollment['prerequisites'])); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($enrollment['syllabus']): ?>
                                    <div class="mb-4">
                                        <h6 class="fw-bold">منهج الدورة:</h6>
                                        <p><?php echo nl2br(htmlspecialchars($enrollment['syllabus'])); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($enrollment['instructor_bio']): ?>
                                    <div class="mb-4">
                                        <h6 class="fw-bold">نبذة عن المدرب:</h6>
                                        <p><?php echo nl2br(htmlspecialchars($enrollment['instructor_bio'])); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="fw-bold">معلومات إضافية:</h6>
                                        <ul class="list-unstyled">
                                            <li><strong>المدة:</strong> <?php echo $enrollment['duration_hours']; ?> ساعة</li>
                                            <li><strong>السعر:</strong> <?php echo $enrollment['price'] > 0 ? number_format($enrollment['price']) . ' ريال' : 'مجانية'; ?></li>
                                            <li><strong>تاريخ التسجيل:</strong> <?php echo date('d/m/Y', strtotime($enrollment['created_at'])); ?></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
