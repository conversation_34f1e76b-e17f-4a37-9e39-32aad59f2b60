<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('admin');

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitize_input($_POST['name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone']);
    $password = $_POST['password'];
    $role = sanitize_input($_POST['role']);
    $status = sanitize_input($_POST['status']);
    $bio = sanitize_input($_POST['bio']);
    $specialization = sanitize_input($_POST['specialization']);

    // Handle profile image upload
    $profile_image = null;
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif'];
        $upload_result = uploadFile($_FILES['profile_image'], 'uploads/avatars/', $allowed_types, 5 * 1024 * 1024);

        if ($upload_result['success']) {
            $profile_image = $upload_result['file_path'];
        } else {
            $error = $upload_result['error'];
        }
    }

    if (empty($name) || empty($email) || empty($password) || empty($role)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!isValidEmail($email)) {
        $error = 'يرجى إدخال بريد إلكتروني صحيح';
    } elseif (strlen($password) < 8) {
        $error = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    } else {
        try {
            // Check if email already exists
            $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->fetch()) {
                $error = 'البريد الإلكتروني مسجل مسبقاً';
            } else {
                $hashed_password = hashPassword($password);
                
                $stmt = $conn->prepare("
                    INSERT INTO users (name, email, phone, password, role, status, bio, specialization, profile_image, email_verified)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                ");

                if ($stmt->execute([$name, $email, $phone, $hashed_password, $role, $status, $bio, $specialization, $profile_image])) {
                    $user_id = $conn->lastInsertId();
                    
                    // Log activity
                    $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, resource_type, resource_id, description, ip_address) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$_SESSION['user_id'], 'create', 'user', $user_id, "إضافة مستخدم جديد: $name ($role)", $_SERVER['REMOTE_ADDR']]);
                    
                    // Send welcome notification
                    $stmt = $conn->prepare("
                        INSERT INTO notifications (user_id, title, message, type) 
                        VALUES (?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $user_id, 
                        'مرحباً بك في منصة التدريب', 
                        'تم إنشاء حسابك بنجاح من قبل الإدارة. يمكنك الآن تسجيل الدخول والبدء في استخدام المنصة.', 
                        'success'
                    ]);
                    
                    $_SESSION['flash_message'] = 'تم إضافة المستخدم بنجاح';
                    $_SESSION['flash_type'] = 'success';
                    redirect('users.php');
                } else {
                    $error = 'حدث خطأ في إضافة المستخدم';
                }
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى';
        }
    }
}

$page_title = 'إضافة مستخدم جديد';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                إدارة الدورات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="users.php">
                                <i class="fas fa-users"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="enrollments.php">
                                <i class="fas fa-user-graduate"></i>
                                التسجيلات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="categories.php">
                                <i class="fas fa-tags"></i>
                                التصنيفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="calendar.php">
                                <i class="fas fa-calendar"></i>
                                التقويم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">إضافة مستخدم جديد</h1>
                    <a href="users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمستخدمين
                    </a>
                </div>

                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-body">
                                <?php if ($error): ?>
                                    <?php echo showAlert($error, 'danger'); ?>
                                <?php endif; ?>
                                
                                <?php if ($success): ?>
                                    <?php echo showAlert($success, 'success'); ?>
                                <?php endif; ?>

                                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" 
                                                   required>
                                        </div>

                                        <div class="col-md-6">
                                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                                   required>
                                        </div>

                                        <div class="col-md-6">
                                            <label for="phone" class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="phone" name="phone" 
                                                   value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                                        </div>

                                        <div class="col-md-6">
                                            <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="password" name="password" 
                                                       minlength="8" required>
                                                <button type="button" class="btn btn-outline-secondary password-toggle" data-target="#password">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div class="form-text">كلمة المرور يجب أن تكون 8 أحرف على الأقل</div>
                                        </div>

                                        <div class="col-md-6">
                                            <label for="role" class="form-label">الدور <span class="text-danger">*</span></label>
                                            <select class="form-select" id="role" name="role" required>
                                                <option value="">اختر الدور</option>
                                                <option value="student" <?php echo (isset($_POST['role']) && $_POST['role'] == 'student') ? 'selected' : ''; ?>>طالب</option>
                                                <option value="instructor" <?php echo (isset($_POST['role']) && $_POST['role'] == 'instructor') ? 'selected' : ''; ?>>مدرب</option>
                                                <option value="admin" <?php echo (isset($_POST['role']) && $_POST['role'] == 'admin') ? 'selected' : ''; ?>>مدير</option>
                                            </select>
                                        </div>

                                        <div class="col-md-6">
                                            <label for="status" class="form-label">الحالة</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="active" <?php echo (isset($_POST['status']) && $_POST['status'] == 'active') ? 'selected' : 'selected'; ?>>نشط</option>
                                                <option value="inactive" <?php echo (isset($_POST['status']) && $_POST['status'] == 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                                                <option value="suspended" <?php echo (isset($_POST['status']) && $_POST['status'] == 'suspended') ? 'selected' : ''; ?>>محظور</option>
                                            </select>
                                        </div>

                                        <div class="col-12">
                                            <label for="profile_image" class="form-label">الصورة الشخصية</label>
                                            <input type="file" class="form-control" id="profile_image" name="profile_image"
                                                   accept="image/*">
                                            <div class="form-text">اختر صورة شخصية للمستخدم (اختياري)</div>
                                        </div>

                                        <div class="col-12" id="specialization-field" style="display: none;">
                                            <label for="specialization" class="form-label">التخصص</label>
                                            <input type="text" class="form-control" id="specialization" name="specialization" 
                                                   value="<?php echo isset($_POST['specialization']) ? htmlspecialchars($_POST['specialization']) : ''; ?>" 
                                                   placeholder="مثال: تطوير الويب، أمن المعلومات، إلخ...">
                                        </div>

                                        <div class="col-12" id="bio-field" style="display: none;">
                                            <label for="bio" class="form-label">نبذة شخصية</label>
                                            <textarea class="form-control" id="bio" name="bio" rows="4" 
                                                      placeholder="نبذة مختصرة عن المدرب وخبراته..."><?php echo isset($_POST['bio']) ? htmlspecialchars($_POST['bio']) : ''; ?></textarea>
                                        </div>

                                        <div class="col-12">
                                            <hr>
                                            <div class="d-flex justify-content-between">
                                                <a href="users.php" class="btn btn-outline-secondary">
                                                    <i class="fas fa-times me-2"></i>إلغاء
                                                </a>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-user-plus me-2"></i>إضافة المستخدم
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
// Show/hide fields based on role
document.getElementById('role').addEventListener('change', function() {
    const role = this.value;
    const specializationField = document.getElementById('specialization-field');
    const bioField = document.getElementById('bio-field');
    
    if (role === 'instructor') {
        specializationField.style.display = 'block';
        bioField.style.display = 'block';
        document.getElementById('specialization').required = true;
    } else {
        specializationField.style.display = 'none';
        bioField.style.display = 'none';
        document.getElementById('specialization').required = false;
        document.getElementById('specialization').value = '';
        document.getElementById('bio').value = '';
    }
});

// Trigger on page load if role is already selected
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('role').dispatchEvent(new Event('change'));
});

// Generate password
function generatePassword() {
    const length = 12;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    document.getElementById('password').value = password;
}

// Add generate password button
document.addEventListener('DOMContentLoaded', function() {
    const passwordGroup = document.querySelector('#password').closest('.input-group');
    const generateBtn = document.createElement('button');
    generateBtn.type = 'button';
    generateBtn.className = 'btn btn-outline-primary';
    generateBtn.innerHTML = '<i class="fas fa-key"></i>';
    generateBtn.title = 'توليد كلمة مرور';
    generateBtn.onclick = generatePassword;
    
    passwordGroup.appendChild(generateBtn);
});
</script>

<?php include '../includes/footer.php'; ?>