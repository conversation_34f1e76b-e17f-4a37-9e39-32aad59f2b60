<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

if (!isLoggedIn()) {
    redirect('auth/login.php');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];

// Handle mark as read
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['mark_read'])) {
    $notification_id = intval($_POST['notification_id']);
    
    try {
        $stmt = $conn->prepare("UPDATE notifications SET is_read = 1, read_at = NOW() WHERE id = ? AND user_id = ?");
        $stmt->execute([$notification_id, $user_id]);
        
        echo json_encode(['success' => true]);
        exit;
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'error' => 'Database error']);
        exit;
    }
}

// Handle mark all as read
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['mark_all_read'])) {
    try {
        $stmt = $conn->prepare("UPDATE notifications SET is_read = 1, read_at = NOW() WHERE user_id = ? AND is_read = 0");
        $stmt->execute([$user_id]);
        
        echo json_encode(['success' => true]);
        exit;
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'error' => 'Database error']);
        exit;
    }
}

// Handle delete notification
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_notification'])) {
    $notification_id = intval($_POST['notification_id']);
    
    try {
        $stmt = $conn->prepare("DELETE FROM notifications WHERE id = ? AND user_id = ?");
        $stmt->execute([$notification_id, $user_id]);
        
        echo json_encode(['success' => true]);
        exit;
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'error' => 'Database error']);
        exit;
    }
}

// Get filter parameters
$filter = isset($_GET['filter']) ? sanitize_input($_GET['filter']) : 'all';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query conditions
$where_conditions = ["user_id = ?"];
$params = [$user_id];

if ($filter === 'unread') {
    $where_conditions[] = "is_read = 0";
} elseif ($filter === 'read') {
    $where_conditions[] = "is_read = 1";
} elseif (in_array($filter, ['info', 'success', 'warning', 'error'])) {
    $where_conditions[] = "type = ?";
    $params[] = $filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count
$count_stmt = $conn->prepare("SELECT COUNT(*) FROM notifications WHERE $where_clause");
$count_stmt->execute($params);
$total_notifications = $count_stmt->fetchColumn();
$total_pages = ceil($total_notifications / $per_page);

// Get notifications
$notifications_stmt = $conn->prepare("
    SELECT * FROM notifications 
    WHERE $where_clause 
    ORDER BY created_at DESC 
    LIMIT $per_page OFFSET $offset
");
$notifications_stmt->execute($params);
$notifications = $notifications_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get unread count
$unread_stmt = $conn->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = 0");
$unread_stmt->execute([$user_id]);
$unread_count = $unread_stmt->fetchColumn();

$page_title = 'الإشعارات';
include 'includes/header.php';
?>

<div class="container my-5">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <!-- Filters Sidebar -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="mb-0 fw-bold">
                        <i class="fas fa-filter me-2"></i>
                        تصفية الإشعارات
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="?filter=all" class="list-group-item list-group-item-action <?php echo $filter === 'all' ? 'active' : ''; ?>">
                            <i class="fas fa-bell me-2"></i>
                            جميع الإشعارات
                            <span class="badge bg-secondary float-end"><?php echo $total_notifications; ?></span>
                        </a>
                        <a href="?filter=unread" class="list-group-item list-group-item-action <?php echo $filter === 'unread' ? 'active' : ''; ?>">
                            <i class="fas fa-bell-slash me-2"></i>
                            غير مقروءة
                            <span class="badge bg-danger float-end"><?php echo $unread_count; ?></span>
                        </a>
                        <a href="?filter=read" class="list-group-item list-group-item-action <?php echo $filter === 'read' ? 'active' : ''; ?>">
                            <i class="fas fa-check me-2"></i>
                            مقروءة
                            <span class="badge bg-success float-end"><?php echo $total_notifications - $unread_count; ?></span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Notification Types -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0 fw-bold">
                        <i class="fas fa-tags me-2"></i>
                        حسب النوع
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="?filter=info" class="list-group-item list-group-item-action <?php echo $filter === 'info' ? 'active' : ''; ?>">
                            <i class="fas fa-info-circle me-2 text-info"></i>
                            معلومات
                        </a>
                        <a href="?filter=success" class="list-group-item list-group-item-action <?php echo $filter === 'success' ? 'active' : ''; ?>">
                            <i class="fas fa-check-circle me-2 text-success"></i>
                            نجاح
                        </a>
                        <a href="?filter=warning" class="list-group-item list-group-item-action <?php echo $filter === 'warning' ? 'active' : ''; ?>">
                            <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                            تحذير
                        </a>
                        <a href="?filter=error" class="list-group-item list-group-item-action <?php echo $filter === 'error' ? 'active' : ''; ?>">
                            <i class="fas fa-times-circle me-2 text-danger"></i>
                            خطأ
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-9">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 fw-bold">
                    <i class="fas fa-bell me-2"></i>
                    الإشعارات
                    <?php if ($unread_count > 0): ?>
                        <span class="badge bg-danger"><?php echo $unread_count; ?></span>
                    <?php endif; ?>
                </h1>
                <div class="d-flex gap-2">
                    <?php if ($unread_count > 0): ?>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="markAllAsRead()">
                            <i class="fas fa-check-double me-2"></i>
                            تحديد الكل كمقروء
                        </button>
                    <?php endif; ?>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <!-- Notifications List -->
            <?php if (empty($notifications)): ?>
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <h5>لا توجد إشعارات</h5>
                        <p class="text-muted">
                            <?php if ($filter === 'unread'): ?>
                                جميع إشعاراتك مقروءة
                            <?php elseif ($filter === 'read'): ?>
                                لا توجد إشعارات مقروءة
                            <?php else: ?>
                                لم تتلق أي إشعارات بعد
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            <?php else: ?>
                <div class="notifications-list">
                    <?php foreach ($notifications as $notification): ?>
                        <div class="card notification-item mb-3 <?php echo !$notification['is_read'] ? 'unread' : ''; ?>" 
                             data-notification-id="<?php echo $notification['id']; ?>">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="notification-content flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-<?php 
                                                echo match($notification['type']) {
                                                    'success' => 'check-circle text-success',
                                                    'warning' => 'exclamation-triangle text-warning',
                                                    'error' => 'times-circle text-danger',
                                                    default => 'info-circle text-info'
                                                };
                                            ?> me-2"></i>
                                            <h6 class="mb-0 fw-bold"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                            <?php if (!$notification['is_read']): ?>
                                                <span class="badge bg-primary ms-2">جديد</span>
                                            <?php endif; ?>
                                        </div>
                                        <p class="mb-2 text-muted"><?php echo htmlspecialchars($notification['message']); ?></p>
                                        <div class="notification-meta d-flex align-items-center text-muted">
                                            <small>
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo timeAgo($notification['created_at']); ?>
                                            </small>
                                            <?php if ($notification['is_read'] && $notification['read_at']): ?>
                                                <small class="ms-3">
                                                    <i class="fas fa-check me-1"></i>
                                                    قُرئ <?php echo timeAgo($notification['read_at']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="notification-actions">
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary" type="button" 
                                                    data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <?php if (!$notification['is_read']): ?>
                                                    <li>
                                                        <a class="dropdown-item" href="#" 
                                                           onclick="markAsRead(<?php echo $notification['id']; ?>)">
                                                            <i class="fas fa-check me-2"></i>
                                                            تحديد كمقروء
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                                <?php if ($notification['action_url']): ?>
                                                    <li>
                                                        <a class="dropdown-item" href="<?php echo htmlspecialchars($notification['action_url']); ?>">
                                                            <i class="fas fa-external-link-alt me-2"></i>
                                                            عرض التفاصيل
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" 
                                                       onclick="deleteNotification(<?php echo $notification['id']; ?>)">
                                                        <i class="fas fa-trash me-2"></i>
                                                        حذف
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if ($notification['action_url']): ?>
                                    <div class="mt-3">
                                        <a href="<?php echo htmlspecialchars($notification['action_url']); ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-arrow-left me-2"></i>
                                            عرض التفاصيل
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="صفحات الإشعارات" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&filter=<?php echo $filter; ?>">
                                        السابق
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&filter=<?php echo $filter; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&filter=<?php echo $filter; ?>">
                                        التالي
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.notification-item {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.notification-item.unread {
    border-left-color: #0d6efd;
    background-color: #f8f9ff;
}

.notification-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notification-content h6 {
    color: #333;
}

.notification-meta {
    font-size: 0.875rem;
}

.list-group-item.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
}
</style>

<script>
function markAsRead(notificationId) {
    fetch('notifications.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `mark_read=1&notification_id=${notificationId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            notificationElement.classList.remove('unread');
            
            // Update badge
            const badge = document.querySelector('.badge.bg-danger');
            if (badge) {
                const count = parseInt(badge.textContent) - 1;
                if (count > 0) {
                    badge.textContent = count;
                } else {
                    badge.remove();
                }
            }
            
            // Refresh page to update counts
            setTimeout(() => location.reload(), 1000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في تحديث الإشعار');
    });
}

function markAllAsRead() {
    if (confirm('هل تريد تحديد جميع الإشعارات كمقروءة؟')) {
        fetch('notifications.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'mark_all_read=1'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في تحديث الإشعارات');
        });
    }
}

function deleteNotification(notificationId) {
    if (confirm('هل تريد حذف هذا الإشعار؟')) {
        fetch('notifications.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `delete_notification=1&notification_id=${notificationId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
                notificationElement.remove();
                
                // Refresh page to update counts
                setTimeout(() => location.reload(), 500);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في حذف الإشعار');
        });
    }
}

// Auto-refresh notifications every 30 seconds
setInterval(() => {
    // Only refresh if user is on the page and not interacting
    if (document.visibilityState === 'visible' && !document.querySelector('.dropdown.show')) {
        location.reload();
    }
}, 30000);
</script>

<?php include 'includes/footer.php'; ?>
