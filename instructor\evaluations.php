<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('instructor');

$instructor_id = $_SESSION['user_id'];
$course_id = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;
$error = '';
$success = '';

// Handle evaluation creation
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_evaluation'])) {
    $course_id = (int)$_POST['course_id'];
    $title = sanitize_input($_POST['title']);
    $description = sanitize_input($_POST['description']);
    $evaluation_type = $_POST['evaluation_type'];
    $max_score = (float)$_POST['max_score'];
    $evaluation_date = $_POST['evaluation_date'];
    
    if (empty($title) || empty($course_id) || empty($evaluation_type) || $max_score <= 0) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            // Verify instructor owns the course
            $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $instructor_id]);
            if (!$stmt->fetch()) {
                $error = 'غير مصرح لك بإنشاء تقييمات لهذه الدورة';
            } else {
                // Get all enrolled students for this course
                $students_stmt = $conn->prepare("
                    SELECT user_id FROM enrollments 
                    WHERE course_id = ? AND status IN ('active', 'completed')
                ");
                $students_stmt->execute([$course_id]);
                $students = $students_stmt->fetchAll(PDO::FETCH_COLUMN);
                
                // Create evaluation for each student
                $stmt = $conn->prepare("
                    INSERT INTO evaluations (user_id, course_id, instructor_id, evaluation_type, title, description, max_score, evaluation_date) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                foreach ($students as $student_id) {
                    $stmt->execute([$student_id, $course_id, $instructor_id, $evaluation_type, $title, $description, $max_score, $evaluation_date]);
                }
                
                logActivity($instructor_id, 'evaluation_create', "إنشاء تقييم: $title");
                $success = 'تم إنشاء التقييم بنجاح لجميع الطلاب';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ في إنشاء التقييم';
        }
    }
}

// Handle grading
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['grade_evaluation'])) {
    $evaluation_id = (int)$_POST['evaluation_id'];
    $score = (float)$_POST['score'];
    $grade = sanitize_input($_POST['grade']);
    $feedback = sanitize_input($_POST['feedback']);
    
    try {
        // Verify instructor owns this evaluation
        $stmt = $conn->prepare("
            SELECT ev.max_score FROM evaluations ev 
            WHERE ev.id = ? AND ev.instructor_id = ?
        ");
        $stmt->execute([$evaluation_id, $instructor_id]);
        $evaluation = $stmt->fetch();
        
        if (!$evaluation) {
            $error = 'غير مصرح لك بتقييم هذا العنصر';
        } elseif ($score > $evaluation['max_score']) {
            $error = 'الدرجة لا يمكن أن تكون أكبر من الدرجة العظمى';
        } else {
            $stmt = $conn->prepare("
                UPDATE evaluations 
                SET score = ?, grade = ?, feedback = ?, graded_at = NOW(), status = 'graded'
                WHERE id = ?
            ");
            $stmt->execute([$score, $grade, $feedback, $evaluation_id]);
            
            logActivity($instructor_id, 'evaluation_grade', "تقييم طالب");
            $success = 'تم حفظ التقييم بنجاح';
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ في حفظ التقييم';
    }
}

// Get instructor's courses
$courses_stmt = $conn->prepare("SELECT id, title FROM courses WHERE instructor_id = ? AND status = 'active' ORDER BY title");
$courses_stmt->execute([$instructor_id]);
$instructor_courses = $courses_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get evaluations with filters
$evaluations_query = "
    SELECT ev.*, u.name as student_name, u.email as student_email, c.title as course_title
    FROM evaluations ev
    JOIN users u ON ev.user_id = u.id
    JOIN courses c ON ev.course_id = c.id
    WHERE ev.instructor_id = ?
";

$params = [$instructor_id];

if ($course_id > 0) {
    $evaluations_query .= " AND ev.course_id = ?";
    $params[] = $course_id;
}

$evaluations_query .= " ORDER BY ev.created_at DESC, ev.evaluation_date DESC";

$evaluations_stmt = $conn->prepare($evaluations_query);
$evaluations_stmt->execute($params);
$evaluations = $evaluations_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'التقييمات';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                دوراتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="sessions.php">
                                <i class="fas fa-calendar-alt"></i>
                                الجلسات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate"></i>
                                الطلاب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="evaluations.php">
                                <i class="fas fa-clipboard-check"></i>
                                التقييمات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">التقييمات</h1>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createEvaluationModal">
                        <i class="fas fa-plus me-2"></i>إنشاء تقييم جديد
                    </button>
                </div>

                <?php if ($error): ?>
                    <?php echo showAlert($error, 'danger'); ?>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <?php echo showAlert($success, 'success'); ?>
                <?php endif; ?>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="course_id" class="form-label">الدورة</label>
                                <select class="form-select" id="course_id" name="course_id">
                                    <option value="">جميع الدورات</option>
                                    <?php foreach ($instructor_courses as $course): ?>
                                        <option value="<?php echo $course['id']; ?>" 
                                                <?php echo $course_id == $course['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($course['title']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-2"></i>بحث
                                    </button>
                                    <a href="evaluations.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Evaluations List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة التقييمات (<?php echo count($evaluations); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($evaluations)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                                <h5>لا توجد تقييمات</h5>
                                <p class="text-muted">لم تقم بإنشاء أي تقييمات بعد</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createEvaluationModal">
                                    <i class="fas fa-plus me-2"></i>إنشاء أول تقييم
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>التقييم</th>
                                            <th>الطالب</th>
                                            <th>الدورة</th>
                                            <th>النوع</th>
                                            <th>الدرجة</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($evaluations as $eval): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($eval['title']); ?></strong>
                                                    <?php if ($eval['description']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($eval['description']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($eval['student_name']); ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($eval['student_email']); ?></small>
                                                </td>
                                                <td><?php echo htmlspecialchars($eval['course_title']); ?></td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php 
                                                        echo match($eval['evaluation_type']) {
                                                            'quiz' => 'اختبار',
                                                            'assignment' => 'واجب',
                                                            'project' => 'مشروع',
                                                            'final' => 'نهائي',
                                                            'participation' => 'مشاركة',
                                                            default => $eval['evaluation_type']
                                                        };
                                                        ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($eval['score'] !== null): ?>
                                                        <strong><?php echo $eval['score']; ?></strong> / <?php echo $eval['max_score']; ?>
                                                        <br><small class="text-muted"><?php echo round(($eval['score'] / $eval['max_score']) * 100, 1); ?>%</small>
                                                    <?php else: ?>
                                                        <span class="text-muted">لم يتم التقييم</span>
                                                        <br><small class="text-muted">من <?php echo $eval['max_score']; ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($eval['score'] !== null): ?>
                                                        <span class="badge bg-success">مقيم</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">معلق</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-outline-primary btn-sm" 
                                                            data-bs-toggle="modal" data-bs-target="#gradeModal"
                                                            onclick="openGradeModal(<?php echo $eval['id']; ?>, '<?php echo addslashes($eval['title']); ?>', '<?php echo addslashes($eval['student_name']); ?>', <?php echo $eval['max_score']; ?>, <?php echo $eval['score'] ?? 'null'; ?>, '<?php echo addslashes($eval['grade'] ?? ''); ?>', '<?php echo addslashes($eval['feedback'] ?? ''); ?>')">
                                                        <i class="fas fa-edit"></i> تقييم
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Create Evaluation Modal -->
<div class="modal fade" id="createEvaluationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء تقييم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="course_id" class="form-label">الدورة *</label>
                            <select class="form-select" id="course_id" name="course_id" required>
                                <option value="">اختر الدورة</option>
                                <?php foreach ($instructor_courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>">
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="evaluation_type" class="form-label">نوع التقييم *</label>
                            <select class="form-select" id="evaluation_type" name="evaluation_type" required>
                                <option value="">اختر النوع</option>
                                <option value="quiz">اختبار</option>
                                <option value="assignment">واجب</option>
                                <option value="project">مشروع</option>
                                <option value="final">اختبار نهائي</option>
                                <option value="participation">مشاركة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان التقييم *</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف التقييم</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max_score" class="form-label">الدرجة العظمى *</label>
                            <input type="number" class="form-control" id="max_score" name="max_score" 
                                   min="1" step="0.5" value="100" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="evaluation_date" class="form-label">تاريخ التقييم</label>
                            <input type="date" class="form-control" id="evaluation_date" name="evaluation_date">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="create_evaluation" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>إنشاء التقييم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Grade Modal -->
<div class="modal fade" id="gradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تقييم الطالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" id="grade_evaluation_id" name="evaluation_id">
                    
                    <div class="mb-3">
                        <label class="form-label">التقييم:</label>
                        <p id="grade_evaluation_title" class="fw-bold"></p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الطالب:</label>
                        <p id="grade_student_name" class="fw-bold"></p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="score" class="form-label">الدرجة *</label>
                            <input type="number" class="form-control" id="score" name="score" 
                                   min="0" step="0.5" required>
                            <div class="form-text">من <span id="max_score_display"></span></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="grade" class="form-label">التقدير</label>
                            <select class="form-select" id="grade" name="grade">
                                <option value="">اختر التقدير</option>
                                <option value="A+">ممتاز مرتفع (A+)</option>
                                <option value="A">ممتاز (A)</option>
                                <option value="B+">جيد جداً مرتفع (B+)</option>
                                <option value="B">جيد جداً (B)</option>
                                <option value="C+">جيد مرتفع (C+)</option>
                                <option value="C">جيد (C)</option>
                                <option value="D+">مقبول مرتفع (D+)</option>
                                <option value="D">مقبول (D)</option>
                                <option value="F">راسب (F)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="feedback" class="form-label">ملاحظات وتعليقات</label>
                        <textarea class="form-control" id="feedback" name="feedback" rows="3" 
                                  placeholder="اكتب ملاحظاتك وتعليقاتك للطالب..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="grade_evaluation" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ التقييم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openGradeModal(evaluationId, title, studentName, maxScore, currentScore, currentGrade, currentFeedback) {
    document.getElementById('grade_evaluation_id').value = evaluationId;
    document.getElementById('grade_evaluation_title').textContent = title;
    document.getElementById('grade_student_name').textContent = studentName;
    document.getElementById('max_score_display').textContent = maxScore;
    document.getElementById('score').setAttribute('max', maxScore);
    
    if (currentScore !== null) {
        document.getElementById('score').value = currentScore;
    }
    if (currentGrade) {
        document.getElementById('grade').value = currentGrade;
    }
    if (currentFeedback) {
        document.getElementById('feedback').value = currentFeedback;
    }
}

// Auto-calculate grade based on score
document.getElementById('score')?.addEventListener('input', function() {
    const score = parseFloat(this.value);
    const maxScore = parseFloat(this.getAttribute('max'));
    const percentage = (score / maxScore) * 100;
    
    const gradeSelect = document.getElementById('grade');
    if (percentage >= 95) gradeSelect.value = 'A+';
    else if (percentage >= 90) gradeSelect.value = 'A';
    else if (percentage >= 85) gradeSelect.value = 'B+';
    else if (percentage >= 80) gradeSelect.value = 'B';
    else if (percentage >= 75) gradeSelect.value = 'C+';
    else if (percentage >= 70) gradeSelect.value = 'C';
    else if (percentage >= 65) gradeSelect.value = 'D+';
    else if (percentage >= 60) gradeSelect.value = 'D';
    else gradeSelect.value = 'F';
});
</script>

<?php include '../includes/footer.php'; ?>
