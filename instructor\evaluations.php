<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('instructor');

$user_id = $_SESSION['user_id'];
$course_id = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;

// Get instructor's courses
$courses_stmt = $conn->prepare("SELECT id, title FROM courses WHERE instructor_id = ? AND status = 'active' ORDER BY title");
$courses_stmt->execute([$user_id]);
$instructor_courses = $courses_stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle evaluation creation
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_evaluation'])) {
    $course_id = intval($_POST['course_id']);
    $student_id = intval($_POST['student_id']);
    $evaluation_type = sanitize_input($_POST['evaluation_type']);
    $title = sanitize_input($_POST['title']);
    $description = sanitize_input($_POST['description']);
    $max_score = floatval($_POST['max_score']);
    $evaluation_date = $_POST['evaluation_date'];
    
    if (empty($title) || empty($evaluation_type) || $max_score <= 0) {
        $_SESSION['flash_message'] = 'يرجى ملء جميع الحقول المطلوبة';
        $_SESSION['flash_type'] = 'danger';
    } else {
        try {
            $stmt = $conn->prepare("
                INSERT INTO evaluations (user_id, course_id, instructor_id, evaluation_type, title, description, max_score, evaluation_date, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')
            ");
            $stmt->execute([$student_id, $course_id, $user_id, $evaluation_type, $title, $description, $max_score, $evaluation_date]);
            
            $_SESSION['flash_message'] = 'تم إنشاء التقييم بنجاح';
            $_SESSION['flash_type'] = 'success';
        } catch (PDOException $e) {
            $_SESSION['flash_message'] = 'حدث خطأ في إنشاء التقييم';
            $_SESSION['flash_type'] = 'danger';
        }
    }
    
    redirect("evaluations.php" . ($course_id ? "?course_id=$course_id" : ""));
}

// Handle grade submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['submit_grade'])) {
    $evaluation_id = intval($_POST['evaluation_id']);
    $score = floatval($_POST['score']);
    $grade = sanitize_input($_POST['grade']);
    $feedback = sanitize_input($_POST['feedback']);
    
    try {
        $stmt = $conn->prepare("
            UPDATE evaluations 
            SET score = ?, grade = ?, feedback = ?, graded_at = NOW(), status = 'graded' 
            WHERE id = ? AND instructor_id = ?
        ");
        $stmt->execute([$score, $grade, $feedback, $evaluation_id, $user_id]);
        
        // Send notification to student
        $eval_stmt = $conn->prepare("SELECT user_id, title FROM evaluations WHERE id = ?");
        $eval_stmt->execute([$evaluation_id]);
        $evaluation = $eval_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($evaluation) {
            $notification_stmt = $conn->prepare("
                INSERT INTO notifications (user_id, title, message, type) 
                VALUES (?, ?, ?, ?)
            ");
            $notification_stmt->execute([
                $evaluation['user_id'], 
                'تم تقييم عملك', 
                "تم تقييم '{$evaluation['title']}' وحصلت على درجة: $grade", 
                'info'
            ]);
        }
        
        $_SESSION['flash_message'] = 'تم حفظ الدرجة بنجاح';
        $_SESSION['flash_type'] = 'success';
    } catch (PDOException $e) {
        $_SESSION['flash_message'] = 'حدث خطأ في حفظ الدرجة';
        $_SESSION['flash_type'] = 'danger';
    }
    
    redirect("evaluations.php" . ($course_id ? "?course_id=$course_id" : ""));
}

// Build query for evaluations
$where_conditions = ["e.instructor_id = ?"];
$params = [$user_id];

if ($course_id > 0) {
    $where_conditions[] = "e.course_id = ?";
    $params[] = $course_id;
}

$where_clause = implode(' AND ', $where_conditions);

// Get evaluations
$evaluations_stmt = $conn->prepare("
    SELECT e.*, u.name as student_name, c.title as course_title 
    FROM evaluations e 
    JOIN users u ON e.user_id = u.id 
    JOIN courses c ON e.course_id = c.id 
    WHERE $where_clause 
    ORDER BY e.evaluation_date DESC, e.created_at DESC
");
$evaluations_stmt->execute($params);
$evaluations = $evaluations_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get students for selected course (for creating new evaluations)
$students = [];
if ($course_id > 0) {
    $students_stmt = $conn->prepare("
        SELECT u.id, u.name 
        FROM users u 
        JOIN enrollments en ON u.id = en.user_id 
        WHERE en.course_id = ? AND en.status = 'active' 
        ORDER BY u.name
    ");
    $students_stmt->execute([$course_id]);
    $students = $students_stmt->fetchAll(PDO::FETCH_ASSOC);
}

$page_title = 'إدارة التقييمات';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                دوراتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate"></i>
                                الطلاب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="evaluations.php">
                                <i class="fas fa-clipboard-check"></i>
                                التقييمات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">إدارة التقييمات</h1>
                    <div class="d-flex gap-2">
                        <?php if ($course_id > 0 && !empty($students)): ?>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createEvaluationModal">
                                <i class="fas fa-plus me-2"></i>إنشاء تقييم جديد
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Course Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3 align-items-end">
                            <div class="col-md-6">
                                <label for="course_id" class="form-label">اختر الدورة</label>
                                <select class="form-select" id="course_id" name="course_id" onchange="this.form.submit()">
                                    <option value="">جميع الدورات</option>
                                    <?php foreach ($instructor_courses as $course): ?>
                                        <option value="<?php echo $course['id']; ?>" 
                                                <?php echo $course_id == $course['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($course['title']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Evaluations List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clipboard-check me-2"></i>
                            التقييمات (<?php echo count($evaluations); ?>)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($evaluations)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                                <h5>لا توجد تقييمات</h5>
                                <p class="text-muted">
                                    <?php if ($course_id > 0): ?>
                                        ابدأ بإنشاء تقييمات للطلاب في هذه الدورة
                                    <?php else: ?>
                                        اختر دورة لعرض التقييمات أو إنشاء تقييمات جديدة
                                    <?php endif; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>التقييم</th>
                                            <th>الطالب</th>
                                            <th>الدورة</th>
                                            <th>النوع</th>
                                            <th>الدرجة</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($evaluations as $evaluation): ?>
                                            <tr>
                                                <td>
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($evaluation['title']); ?></h6>
                                                    <?php if ($evaluation['description']): ?>
                                                        <small class="text-muted"><?php echo htmlspecialchars($evaluation['description']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($evaluation['student_name']); ?></td>
                                                <td><?php echo htmlspecialchars($evaluation['course_title']); ?></td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        <?php
                                                        $types = [
                                                            'quiz' => 'اختبار',
                                                            'assignment' => 'واجب',
                                                            'project' => 'مشروع',
                                                            'final' => 'اختبار نهائي',
                                                            'participation' => 'مشاركة'
                                                        ];
                                                        echo $types[$evaluation['evaluation_type']] ?? $evaluation['evaluation_type'];
                                                        ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($evaluation['score'] !== null): ?>
                                                        <strong><?php echo $evaluation['score']; ?></strong> / <?php echo $evaluation['max_score']; ?>
                                                        <?php if ($evaluation['grade']): ?>
                                                            <br><span class="badge bg-primary"><?php echo $evaluation['grade']; ?></span>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير مقيم</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_classes = [
                                                        'pending' => 'bg-warning',
                                                        'submitted' => 'bg-info',
                                                        'graded' => 'bg-success',
                                                        'reviewed' => 'bg-primary'
                                                    ];
                                                    $status_names = [
                                                        'pending' => 'معلق',
                                                        'submitted' => 'مُسلم',
                                                        'graded' => 'مُقيم',
                                                        'reviewed' => 'مُراجع'
                                                    ];
                                                    ?>
                                                    <span class="badge <?php echo $status_classes[$evaluation['status']] ?? 'bg-secondary'; ?>">
                                                        <?php echo $status_names[$evaluation['status']] ?? $evaluation['status']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo $evaluation['evaluation_date'] ? date('d/m/Y', strtotime($evaluation['evaluation_date'])) : '-'; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary" 
                                                                onclick="gradeEvaluation(<?php echo $evaluation['id']; ?>, '<?php echo htmlspecialchars($evaluation['title']); ?>', <?php echo $evaluation['max_score']; ?>, '<?php echo $evaluation['score'] ?? ''; ?>', '<?php echo $evaluation['grade'] ?? ''; ?>', '<?php echo htmlspecialchars($evaluation['feedback'] ?? ''); ?>')"
                                                                title="تقييم">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-info" 
                                                                onclick="viewEvaluation(<?php echo $evaluation['id']; ?>)"
                                                                title="عرض التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Create Evaluation Modal -->
<?php if ($course_id > 0 && !empty($students)): ?>
<div class="modal fade" id="createEvaluationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء تقييم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="course_id" value="<?php echo $course_id; ?>">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="student_id" class="form-label">الطالب *</label>
                            <select class="form-select" id="student_id" name="student_id" required>
                                <option value="">اختر الطالب</option>
                                <?php foreach ($students as $student): ?>
                                    <option value="<?php echo $student['id']; ?>">
                                        <?php echo htmlspecialchars($student['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="evaluation_type" class="form-label">نوع التقييم *</label>
                            <select class="form-select" id="evaluation_type" name="evaluation_type" required>
                                <option value="">اختر النوع</option>
                                <option value="quiz">اختبار</option>
                                <option value="assignment">واجب</option>
                                <option value="project">مشروع</option>
                                <option value="final">اختبار نهائي</option>
                                <option value="participation">مشاركة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">عنوان التقييم *</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max_score" class="form-label">الدرجة الكاملة *</label>
                            <input type="number" class="form-control" id="max_score" name="max_score" 
                                   min="1" step="0.5" value="100" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="evaluation_date" class="form-label">تاريخ التقييم</label>
                            <input type="date" class="form-control" id="evaluation_date" name="evaluation_date" 
                                   value="<?php echo date('Y-m-d'); ?>">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="create_evaluation" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إنشاء التقييم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Grade Evaluation Modal -->
<div class="modal fade" id="gradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تقييم الطالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="evaluation_id" id="gradeEvaluationId">
                    
                    <div class="mb-3">
                        <h6 id="gradeEvaluationTitle"></h6>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="score" class="form-label">الدرجة المحصلة</label>
                            <input type="number" class="form-control" id="score" name="score" 
                                   min="0" step="0.5">
                            <div class="form-text">من <span id="maxScore"></span></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="grade" class="form-label">التقدير</label>
                            <select class="form-select" id="grade" name="grade">
                                <option value="">اختر التقدير</option>
                                <option value="A+">ممتاز مرتفع (A+)</option>
                                <option value="A">ممتاز (A)</option>
                                <option value="B+">جيد جداً مرتفع (B+)</option>
                                <option value="B">جيد جداً (B)</option>
                                <option value="C+">جيد مرتفع (C+)</option>
                                <option value="C">جيد (C)</option>
                                <option value="D+">مقبول مرتفع (D+)</option>
                                <option value="D">مقبول (D)</option>
                                <option value="F">راسب (F)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="feedback" class="form-label">التعليقات والملاحظات</label>
                        <textarea class="form-control" id="feedback" name="feedback" rows="4"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="submit_grade" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ التقييم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function gradeEvaluation(id, title, maxScore, currentScore, currentGrade, currentFeedback) {
    document.getElementById('gradeEvaluationId').value = id;
    document.getElementById('gradeEvaluationTitle').textContent = title;
    document.getElementById('maxScore').textContent = maxScore;
    document.getElementById('score').setAttribute('max', maxScore);
    document.getElementById('score').value = currentScore;
    document.getElementById('grade').value = currentGrade;
    document.getElementById('feedback').value = currentFeedback;
    
    new bootstrap.Modal(document.getElementById('gradeModal')).show();
}

function viewEvaluation(id) {
    // This would open a detailed view of the evaluation
    // For now, we'll just show an alert
    alert('عرض تفاصيل التقييم رقم: ' + id);
}

// Auto-calculate grade based on score
document.getElementById('score')?.addEventListener('input', function() {
    const score = parseFloat(this.value);
    const maxScore = parseFloat(this.getAttribute('max'));
    const percentage = (score / maxScore) * 100;
    
    let grade = '';
    if (percentage >= 95) grade = 'A+';
    else if (percentage >= 90) grade = 'A';
    else if (percentage >= 85) grade = 'B+';
    else if (percentage >= 80) grade = 'B';
    else if (percentage >= 75) grade = 'C+';
    else if (percentage >= 70) grade = 'C';
    else if (percentage >= 65) grade = 'D+';
    else if (percentage >= 60) grade = 'D';
    else grade = 'F';
    
    document.getElementById('grade').value = grade;
});
</script>

<?php include '../includes/footer.php'; ?>
