<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('admin');

$session_id = isset($_GET['session_id']) ? (int)$_GET['session_id'] : 0;
$error = '';
$success = '';

if (!$session_id) {
    header('Location: calendar.php');
    exit;
}

// Get session details
$session_stmt = $conn->prepare("
    SELECT cs.*, c.title as course_title, c.id as course_id,
           u.name as instructor_name
    FROM course_sessions cs
    JOIN courses c ON cs.course_id = c.id
    LEFT JOIN users u ON c.instructor_id = u.id
    WHERE cs.id = ?
");
$session_stmt->execute([$session_id]);
$session = $session_stmt->fetch(PDO::FETCH_ASSOC);

if (!$session) {
    header('Location: calendar.php');
    exit;
}

// Handle attendance update
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_attendance'])) {
    $attendance_data = $_POST['attendance'] ?? [];
    
    try {
        $conn->beginTransaction();
        
        foreach ($attendance_data as $user_id => $data) {
            $status = $data['status'] ?? 'absent';
            $notes = sanitize_input($data['notes'] ?? '');
            $check_in_time = !empty($data['check_in_time']) ? $data['check_in_time'] : null;
            $check_out_time = !empty($data['check_out_time']) ? $data['check_out_time'] : null;
            
            // Check if attendance record exists
            $check_stmt = $conn->prepare("SELECT id FROM attendance WHERE session_id = ? AND user_id = ?");
            $check_stmt->execute([$session_id, $user_id]);
            
            if ($check_stmt->fetch()) {
                // Update existing record
                $update_stmt = $conn->prepare("
                    UPDATE attendance 
                    SET status = ?, notes = ?, check_in_time = ?, check_out_time = ?, updated_at = NOW() 
                    WHERE session_id = ? AND user_id = ?
                ");
                $update_stmt->execute([$status, $notes, $check_in_time, $check_out_time, $session_id, $user_id]);
            } else {
                // Insert new record
                $insert_stmt = $conn->prepare("
                    INSERT INTO attendance (session_id, user_id, status, notes, check_in_time, check_out_time, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                $insert_stmt->execute([$session_id, $user_id, $status, $notes, $check_in_time, $check_out_time]);
            }
        }
        
        $conn->commit();
        logActivity($_SESSION['user_id'], 'attendance_update', "تحديث حضور جلسة");
        $success = 'تم حفظ بيانات الحضور بنجاح';
        
    } catch (PDOException $e) {
        $conn->rollBack();
        $error = 'حدث خطأ في حفظ بيانات الحضور';
    }
}

// Handle bulk attendance actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selected_students = $_POST['selected_students'] ?? [];
    
    if (!empty($selected_students)) {
        try {
            $conn->beginTransaction();
            
            foreach ($selected_students as $user_id) {
                $check_stmt = $conn->prepare("SELECT id FROM attendance WHERE session_id = ? AND user_id = ?");
                $check_stmt->execute([$session_id, $user_id]);
                
                if ($check_stmt->fetch()) {
                    $update_stmt = $conn->prepare("
                        UPDATE attendance 
                        SET status = ?, updated_at = NOW() 
                        WHERE session_id = ? AND user_id = ?
                    ");
                    $update_stmt->execute([$action, $session_id, $user_id]);
                } else {
                    $insert_stmt = $conn->prepare("
                        INSERT INTO attendance (session_id, user_id, status, created_at) 
                        VALUES (?, ?, ?, NOW())
                    ");
                    $insert_stmt->execute([$session_id, $user_id, $action]);
                }
            }
            
            $conn->commit();
            logActivity($_SESSION['user_id'], 'attendance_bulk', "تحديث جماعي للحضور");
            $success = 'تم تحديث حضور الطلاب المحددين بنجاح';
            
        } catch (PDOException $e) {
            $conn->rollBack();
            $error = 'حدث خطأ في التحديث الجماعي';
        }
    }
}

// Get enrolled students and their attendance
$students_stmt = $conn->prepare("
    SELECT u.id, u.name, u.email, u.phone,
           a.status, a.notes, a.check_in_time, a.check_out_time, a.created_at as attendance_date
    FROM enrollments e
    JOIN users u ON e.user_id = u.id
    LEFT JOIN attendance a ON e.user_id = a.user_id AND a.session_id = ?
    WHERE e.course_id = ? AND e.status IN ('active', 'completed')
    ORDER BY u.name
");
$students_stmt->execute([$session_id, $session['course_id']]);
$students = $students_stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate statistics
$total_students = count($students);
$present_count = count(array_filter($students, fn($s) => $s['status'] === 'present'));
$late_count = count(array_filter($students, fn($s) => $s['status'] === 'late'));
$absent_count = count(array_filter($students, fn($s) => $s['status'] === 'absent'));
$excused_count = count(array_filter($students, fn($s) => $s['status'] === 'excused'));
$not_marked = $total_students - $present_count - $late_count - $absent_count - $excused_count;

$page_title = 'إدارة الحضور - ' . $session['title'];
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users"></i>
                                المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                الدورات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="calendar.php">
                                <i class="fas fa-calendar"></i>
                                التقويم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 fw-bold">إدارة الحضور</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="calendar.php">التقويم</a></li>
                                <li class="breadcrumb-item active">إدارة الحضور</li>
                            </ol>
                        </nav>
                    </div>
                    <a href="calendar.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة للتقويم
                    </a>
                </div>

                <?php if ($error): ?>
                    <?php echo showAlert($error, 'danger'); ?>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <?php echo showAlert($success, 'success'); ?>
                <?php endif; ?>

                <!-- Session Info -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="text-primary mb-2"><?php echo htmlspecialchars($session['title']); ?></h5>
                                <p class="text-muted mb-2">
                                    <strong>الدورة:</strong> <?php echo htmlspecialchars($session['course_title']); ?>
                                </p>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo date('d/m/Y', strtotime($session['session_date'])); ?>
                                        </small>
                                    </div>
                                    <div class="col-sm-6">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo date('H:i', strtotime($session['start_time'])); ?> - 
                                            <?php echo date('H:i', strtotime($session['end_time'])); ?>
                                        </small>
                                    </div>
                                </div>
                                <?php if ($session['instructor_name']): ?>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            المدرب: <?php echo htmlspecialchars($session['instructor_name']); ?>
                                        </small>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4">
                                <!-- Statistics -->
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h6 class="text-success"><?php echo $present_count + $late_count; ?></h6>
                                        <small class="text-muted">حاضر</small>
                                    </div>
                                    <div class="col-6">
                                        <h6 class="text-danger"><?php echo $absent_count; ?></h6>
                                        <small class="text-muted">غائب</small>
                                    </div>
                                </div>
                                <div class="progress mt-2" style="height: 5px;">
                                    <div class="progress-bar bg-success" style="width: <?php echo $total_students > 0 ? round((($present_count + $late_count) / $total_students) * 100) : 0; ?>%"></div>
                                </div>
                                <small class="text-muted">معدل الحضور: <?php echo $total_students > 0 ? round((($present_count + $late_count) / $total_students) * 100) : 0; ?>%</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="POST" id="bulkForm">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <input type="checkbox" id="selectAll" class="form-check-input me-2">
                                        <label for="selectAll" class="form-check-label me-3">تحديد الكل</label>
                                        <select name="bulk_action" class="form-select form-select-sm me-2" style="width: auto;">
                                            <option value="">اختر إجراء</option>
                                            <option value="present">تسجيل حضور</option>
                                            <option value="absent">تسجيل غياب</option>
                                            <option value="late">تسجيل تأخير</option>
                                            <option value="excused">تسجيل عذر</option>
                                        </select>
                                        <button type="submit" class="btn btn-primary btn-sm">تطبيق</button>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <span class="badge bg-primary me-1">المجموع: <?php echo $total_students; ?></span>
                                    <span class="badge bg-success me-1">حاضر: <?php echo $present_count; ?></span>
                                    <span class="badge bg-warning me-1">متأخر: <?php echo $late_count; ?></span>
                                    <span class="badge bg-danger me-1">غائب: <?php echo $absent_count; ?></span>
                                    <span class="badge bg-info me-1">معذور: <?php echo $excused_count; ?></span>
                                    <?php if ($not_marked > 0): ?>
                                        <span class="badge bg-secondary">غير محدد: <?php echo $not_marked; ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Attendance Form -->
                <form method="POST" class="needs-validation" novalidate>
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">قائمة الطلاب</h5>
                        </div>
                        <div class="card-body p-0">
                            <?php if (empty($students)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                                    <h6>لا يوجد طلاب مسجلين</h6>
                                    <p class="text-muted">لا يوجد طلاب مسجلين في هذه الدورة</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="50">
                                                    <input type="checkbox" class="form-check-input" id="selectAllTable">
                                                </th>
                                                <th>الطالب</th>
                                                <th>الحالة</th>
                                                <th>وقت الدخول</th>
                                                <th>وقت الخروج</th>
                                                <th>ملاحظات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($students as $student): ?>
                                                <tr>
                                                    <td>
                                                        <input type="checkbox" name="selected_students[]" 
                                                               value="<?php echo $student['id']; ?>" 
                                                               class="form-check-input student-checkbox">
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($student['name']); ?></strong>
                                                            <br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <select name="attendance[<?php echo $student['id']; ?>][status]" 
                                                                class="form-select form-select-sm">
                                                            <option value="">غير محدد</option>
                                                            <option value="present" <?php echo $student['status'] === 'present' ? 'selected' : ''; ?>>حاضر</option>
                                                            <option value="late" <?php echo $student['status'] === 'late' ? 'selected' : ''; ?>>متأخر</option>
                                                            <option value="absent" <?php echo $student['status'] === 'absent' ? 'selected' : ''; ?>>غائب</option>
                                                            <option value="excused" <?php echo $student['status'] === 'excused' ? 'selected' : ''; ?>>معذور</option>
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <input type="time" name="attendance[<?php echo $student['id']; ?>][check_in_time]" 
                                                               class="form-control form-control-sm"
                                                               value="<?php echo $student['check_in_time'] ? date('H:i', strtotime($student['check_in_time'])) : ''; ?>">
                                                    </td>
                                                    <td>
                                                        <input type="time" name="attendance[<?php echo $student['id']; ?>][check_out_time]" 
                                                               class="form-control form-control-sm"
                                                               value="<?php echo $student['check_out_time'] ? date('H:i', strtotime($student['check_out_time'])) : ''; ?>">
                                                    </td>
                                                    <td>
                                                        <input type="text" name="attendance[<?php echo $student['id']; ?>][notes]" 
                                                               class="form-control form-control-sm" 
                                                               placeholder="ملاحظات..."
                                                               value="<?php echo htmlspecialchars($student['notes'] ?? ''); ?>">
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php if (!empty($students)): ?>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <small class="text-muted">
                                            آخر تحديث: <?php echo $students[0]['attendance_date'] ? date('d/m/Y H:i', strtotime($students[0]['attendance_date'])) : 'لم يتم التحديث'; ?>
                                        </small>
                                    </div>
                                    <div>
                                        <button type="submit" name="update_attendance" class="btn btn-success">
                                            <i class="fas fa-save me-2"></i>حفظ الحضور
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </form>
            </main>
        </div>
    </div>
</div>

<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

document.getElementById('selectAllTable').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Auto-fill check-in time when status is changed to present or late
document.querySelectorAll('select[name*="[status]"]').forEach(select => {
    select.addEventListener('change', function() {
        const row = this.closest('tr');
        const checkInInput = row.querySelector('input[name*="[check_in_time]"]');
        
        if ((this.value === 'present' || this.value === 'late') && !checkInInput.value) {
            const now = new Date();
            const timeString = now.getHours().toString().padStart(2, '0') + ':' + 
                             now.getMinutes().toString().padStart(2, '0');
            checkInInput.value = timeString;
        }
    });
});

// Bulk form validation
document.getElementById('bulkForm').addEventListener('submit', function(e) {
    const selectedStudents = document.querySelectorAll('.student-checkbox:checked');
    const bulkAction = document.querySelector('select[name="bulk_action"]').value;
    
    if (selectedStudents.length === 0) {
        e.preventDefault();
        alert('يرجى تحديد طالب واحد على الأقل');
        return;
    }
    
    if (!bulkAction) {
        e.preventDefault();
        alert('يرجى اختيار إجراء');
        return;
    }
    
    if (!confirm(`هل أنت متأكد من تطبيق هذا الإجراء على ${selectedStudents.length} طالب؟`)) {
        e.preventDefault();
    }
});
</script>

<?php include '../includes/footer.php'; ?>
