<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (isLoggedIn()) {
    $role = $_SESSION['user_role'];
    redirect("../$role/dashboard.php");
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = sanitize_input($_POST['email']);
    
    if (empty($email)) {
        $error = 'يرجى إدخال البريد الإلكتروني';
    } elseif (!isValidEmail($email)) {
        $error = 'يرجى إدخال بريد إلكتروني صحيح';
    } else {
        try {
            // Check if email exists
            $stmt = $conn->prepare("SELECT id, name FROM users WHERE email = ? AND status = 'active'");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                // Generate reset token
                $reset_token = generateToken();
                $reset_expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
                
                // Save reset token
                $stmt = $conn->prepare("UPDATE users SET reset_token = ?, reset_expires = ? WHERE id = ?");
                $stmt->execute([$reset_token, $reset_expires, $user['id']]);
                
                // Log activity
                $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, description, ip_address) VALUES (?, ?, ?, ?)");
                $stmt->execute([$user['id'], 'password_reset_request', 'طلب إعادة تعيين كلمة المرور', $_SERVER['REMOTE_ADDR']]);
                
                // In a real application, you would send an email here
                // For now, we'll just show the reset link
                $reset_link = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/reset-password.php?token=" . $reset_token;
                
                $success = "تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.<br><br>";
                $success .= "<strong>للاختبار فقط:</strong><br>";
                $success .= "<a href='$reset_link' class='btn btn-sm btn-primary'>اضغط هنا لإعادة تعيين كلمة المرور</a>";
            } else {
                // Don't reveal if email exists or not for security
                $success = 'إذا كان البريد الإلكتروني موجود في نظامنا، ستتلقى رابط إعادة تعيين كلمة المرور';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى';
        }
    }
}

$page_title = 'استعادة كلمة المرور';
$base_url = '../';
include '../includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-custom mt-5">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-key fa-3x text-primary mb-3"></i>
                        <h4 class="fw-bold">استعادة كلمة المرور</h4>
                        <p class="text-muted">أدخل بريدك الإلكتروني لاستعادة كلمة المرور</p>
                    </div>
                    
                    <?php if ($error): ?>
                        <?php echo showAlert($error, 'danger'); ?>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <?php echo showAlert($success, 'success'); ?>
                    <?php else: ?>
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <div class="mb-4">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                           placeholder="أدخل بريدك الإلكتروني" required>
                                </div>
                                <div class="form-text">
                                    سنرسل لك رابط إعادة تعيين كلمة المرور على هذا البريد
                                </div>
                            </div>
                            
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال رابط الاستعادة
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                    
                    <div class="text-center">
                        <p class="mb-2">
                            <a href="login.php" class="text-decoration-none">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة لتسجيل الدخول
                            </a>
                        </p>
                        <p class="mb-0">
                            ليس لديك حساب؟ 
                            <a href="register.php" class="text-decoration-none fw-bold">سجل الآن</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h6 class="fw-bold mb-3">
                        <i class="fas fa-info-circle me-2 text-info"></i>
                        معلومات مهمة
                    </h6>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            رابط إعادة التعيين صالح لمدة ساعة واحدة فقط
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            تحقق من مجلد الرسائل غير المرغوب فيها إذا لم تجد الرسالة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            يمكنك طلب رابط جديد إذا انتهت صلاحية الرابط السابق
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            تواصل معنا إذا واجهت أي مشاكل في الاستعادة
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
