<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

if (isLoggedIn()) {
    $role = $_SESSION['user_role'];
    redirect("../$role/dashboard.php");
}

$token = isset($_GET['token']) ? sanitize_input($_GET['token']) : '';
$error = '';
$success = '';
$valid_token = false;
$user = null;

// Validate token
if (!empty($token)) {
    try {
        $stmt = $conn->prepare("
            SELECT id, name, email 
            FROM users 
            WHERE reset_token = ? AND reset_expires > NOW() AND status = 'active'
        ");
        $stmt->execute([$token]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            $valid_token = true;
        } else {
            $error = 'رابط إعادة التعيين غير صحيح أو منتهي الصلاحية';
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى';
    }
} else {
    $error = 'رابط إعادة التعيين غير صحيح';
}

// Handle password reset
if ($_SERVER['REQUEST_METHOD'] == 'POST' && $valid_token) {
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    if (empty($password) || empty($confirm_password)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (strlen($password) < 8) {
        $error = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتان';
    } else {
        try {
            $hashed_password = hashPassword($password);
            
            // Update password and clear reset token
            $stmt = $conn->prepare("
                UPDATE users 
                SET password = ?, reset_token = NULL, reset_expires = NULL 
                WHERE id = ?
            ");
            $stmt->execute([$hashed_password, $user['id']]);
            
            // Log activity
            $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, description, ip_address) VALUES (?, ?, ?, ?)");
            $stmt->execute([$user['id'], 'password_reset', 'إعادة تعيين كلمة المرور', $_SERVER['REMOTE_ADDR']]);
            
            // Send notification
            $stmt = $conn->prepare("
                INSERT INTO notifications (user_id, title, message, type) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $user['id'], 
                'تم تغيير كلمة المرور', 
                'تم تغيير كلمة المرور الخاصة بك بنجاح. إذا لم تقم بهذا الإجراء، يرجى التواصل معنا فوراً.', 
                'success'
            ]);
            
            $success = 'تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة';
            $valid_token = false; // Prevent form from showing again
        } catch (PDOException $e) {
            $error = 'حدث خطأ في تغيير كلمة المرور. يرجى المحاولة مرة أخرى';
        }
    }
}

$page_title = 'إعادة تعيين كلمة المرور';
$base_url = '../';
include '../includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-custom mt-5">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-lock fa-3x text-primary mb-3"></i>
                        <h4 class="fw-bold">إعادة تعيين كلمة المرور</h4>
                        <?php if ($valid_token && $user): ?>
                            <p class="text-muted">مرحباً <?php echo htmlspecialchars($user['name']); ?></p>
                            <p class="text-muted">أدخل كلمة المرور الجديدة</p>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($error): ?>
                        <?php echo showAlert($error, 'danger'); ?>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <?php echo showAlert($success, 'success'); ?>
                        <div class="text-center mt-4">
                            <a href="login.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </a>
                        </div>
                    <?php elseif ($valid_token): ?>
                        <form method="POST" action="" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور الجديدة</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           minlength="8" required>
                                    <button type="button" class="btn btn-outline-secondary password-toggle" data-target="#password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">كلمة المرور يجب أن تكون 8 أحرف على الأقل</div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           minlength="8" required>
                                    <button type="button" class="btn btn-outline-secondary password-toggle" data-target="#confirm_password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ كلمة المرور الجديدة
                                </button>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="text-center">
                            <p class="text-muted mb-4">
                                رابط إعادة التعيين غير صحيح أو منتهي الصلاحية
                            </p>
                            <a href="forgot-password.php" class="btn btn-primary">
                                <i class="fas fa-redo me-2"></i>
                                طلب رابط جديد
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <div class="text-center mt-4">
                        <p class="mb-0">
                            <a href="login.php" class="text-decoration-none">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة لتسجيل الدخول
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h6 class="fw-bold mb-3">
                        <i class="fas fa-shield-alt me-2 text-success"></i>
                        نصائح أمنية
                    </h6>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            استخدم كلمة مرور قوية تحتوي على أحرف وأرقام ورموز
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            لا تشارك كلمة المرور مع أي شخص آخر
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            قم بتغيير كلمة المرور بانتظام
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            تأكد من تسجيل الخروج عند استخدام أجهزة مشتركة
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password')?.addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// Password strength indicator
document.getElementById('password')?.addEventListener('input', function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);
    // You can add visual feedback here
});

function calculatePasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
}
</script>

<?php include '../includes/footer.php'; ?>
