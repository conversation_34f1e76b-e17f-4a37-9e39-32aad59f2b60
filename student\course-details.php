<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('student');

$user_id = $_SESSION['user_id'];
$course_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$course_id) {
    echo '<div class="alert alert-danger">معرف الدورة غير صحيح</div>';
    exit;
}

// Handle enrollment
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['enroll'])) {
    try {
        // Check if already enrolled
        $check_stmt = $conn->prepare("SELECT id FROM enrollments WHERE user_id = ? AND course_id = ?");
        $check_stmt->execute([$user_id, $course_id]);
        
        if ($check_stmt->fetch()) {
            $error = 'أنت مسجل في هذه الدورة مسبقاً';
        } else {
            // Check course capacity
            $capacity_stmt = $conn->prepare("
                SELECT c.max_students, COUNT(e.id) as current_enrollments
                FROM courses c
                LEFT JOIN enrollments e ON c.id = e.course_id AND e.status IN ('active', 'completed')
                WHERE c.id = ?
                GROUP BY c.id
            ");
            $capacity_stmt->execute([$course_id]);
            $capacity = $capacity_stmt->fetch();
            
            if ($capacity && $capacity['current_enrollments'] >= $capacity['max_students']) {
                $error = 'الدورة مكتملة العدد';
            } else {
                // Enroll student
                $enroll_stmt = $conn->prepare("
                    INSERT INTO enrollments (user_id, course_id, status, enrolled_at) 
                    VALUES (?, ?, 'active', NOW())
                ");
                $enroll_stmt->execute([$user_id, $course_id]);
                
                logActivity($user_id, 'course_enroll', "تسجيل في دورة");
                $success = 'تم تسجيلك في الدورة بنجاح!';
            }
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ في التسجيل';
    }
}

// Get course details
$course_stmt = $conn->prepare("
    SELECT c.*, cat.name as category_name,
           u.name as instructor_name, u.email as instructor_email, u.bio as instructor_bio,
           u.profile_image as instructor_image,
           COUNT(DISTINCT e.user_id) as enrolled_students,
           (SELECT COUNT(*) FROM enrollments WHERE user_id = ? AND course_id = c.id) as is_enrolled
    FROM courses c
    LEFT JOIN categories cat ON c.category_id = cat.id
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN enrollments e ON c.id = e.course_id AND e.status IN ('active', 'completed')
    WHERE c.id = ? AND c.status = 'active'
    GROUP BY c.id
");
$course_stmt->execute([$user_id, $course_id]);
$course = $course_stmt->fetch(PDO::FETCH_ASSOC);

if (!$course) {
    echo '<div class="alert alert-danger">الدورة غير موجودة أو غير متاحة</div>';
    exit;
}

// Get course sessions
$sessions_stmt = $conn->prepare("
    SELECT * FROM course_sessions 
    WHERE course_id = ? 
    ORDER BY session_date ASC, start_time ASC
");
$sessions_stmt->execute([$course_id]);
$sessions = $sessions_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get course materials (public only)
$materials_stmt = $conn->prepare("
    SELECT * FROM course_materials 
    WHERE course_id = ? AND (is_public = 1 OR is_public IS NULL)
    ORDER BY sort_order ASC, created_at DESC
    LIMIT 5
");
$materials_stmt->execute([$course_id]);
$materials = $materials_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="row">
    <div class="col-md-8">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <div class="d-flex align-items-start mb-4">
            <?php if ($course['image']): ?>
                <img src="../<?php echo htmlspecialchars($course['image']); ?>" 
                     alt="<?php echo htmlspecialchars($course['title']); ?>"
                     class="rounded me-3" style="width: 100px; height: 100px; object-fit: cover;">
            <?php else: ?>
                <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center me-3" 
                     style="width: 100px; height: 100px;">
                    <i class="fas fa-graduation-cap fa-2x"></i>
                </div>
            <?php endif; ?>
            
            <div class="flex-grow-1">
                <h4 class="text-primary mb-2"><?php echo htmlspecialchars($course['title']); ?></h4>
                <p class="text-muted mb-2"><?php echo htmlspecialchars($course['short_description'] ?? $course['description']); ?></p>
                
                <div class="d-flex flex-wrap gap-2">
                    <span class="badge bg-secondary"><?php echo htmlspecialchars($course['category_name'] ?? 'غير محدد'); ?></span>
                    <span class="badge bg-info"><?php echo htmlspecialchars($course['level'] ?? 'جميع المستويات'); ?></span>
                    <span class="badge bg-warning text-dark"><?php echo $course['duration_hours']; ?> ساعة</span>
                    <?php if ($course['is_enrolled']): ?>
                        <span class="badge bg-success">مسجل</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="mb-4">
            <h6>وصف الدورة:</h6>
            <p><?php echo nl2br(htmlspecialchars($course['description'])); ?></p>
        </div>

        <?php if ($course['objectives']): ?>
            <div class="mb-4">
                <h6>أهداف الدورة:</h6>
                <p><?php echo nl2br(htmlspecialchars($course['objectives'])); ?></p>
            </div>
        <?php endif; ?>

        <?php if ($course['prerequisites']): ?>
            <div class="mb-4">
                <h6>المتطلبات المسبقة:</h6>
                <p><?php echo nl2br(htmlspecialchars($course['prerequisites'])); ?></p>
            </div>
        <?php endif; ?>

        <?php if (!empty($sessions)): ?>
            <div class="mb-4">
                <h6>جدول الجلسات:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>العنوان</th>
                                <th>التاريخ</th>
                                <th>الوقت</th>
                                <th>المكان</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($sessions as $session): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($session['title']); ?></td>
                                    <td><?php echo date('d/m/Y', strtotime($session['session_date'])); ?></td>
                                    <td>
                                        <?php echo date('H:i', strtotime($session['start_time'])); ?> - 
                                        <?php echo date('H:i', strtotime($session['end_time'])); ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($session['location'] ?? 'غير محدد'); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>

        <?php if (!empty($materials)): ?>
            <div class="mb-4">
                <h6>نماذج من المواد التعليمية:</h6>
                <div class="row g-2">
                    <?php foreach ($materials as $material): ?>
                        <div class="col-md-6">
                            <div class="card border">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-<?php 
                                            echo match($material['file_type']) {
                                                'pdf' => 'file-pdf text-danger',
                                                'video' => 'file-video text-primary',
                                                'image' => 'file-image text-success',
                                                default => 'file-alt text-secondary'
                                            };
                                        ?> me-2"></i>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($material['title']); ?></h6>
                                            <small class="text-muted"><?php echo formatFileSize($material['file_size']); ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Instructor Info -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">معلومات المدرب</h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-start">
                    <?php if ($course['instructor_image']): ?>
                        <img src="../<?php echo htmlspecialchars($course['instructor_image']); ?>" 
                             alt="<?php echo htmlspecialchars($course['instructor_name']); ?>"
                             class="rounded-circle me-3" style="width: 60px; height: 60px; object-fit: cover;">
                    <?php else: ?>
                        <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                             style="width: 60px; height: 60px;">
                            <i class="fas fa-user"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div>
                        <h6 class="mb-1"><?php echo htmlspecialchars($course['instructor_name'] ?? 'غير محدد'); ?></h6>
                        <?php if ($course['instructor_email']): ?>
                            <p class="text-muted mb-2"><?php echo htmlspecialchars($course['instructor_email']); ?></p>
                        <?php endif; ?>
                        <?php if ($course['instructor_bio']): ?>
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($course['instructor_bio'])); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card sticky-top" style="top: 20px;">
            <div class="card-body">
                <div class="text-center mb-3">
                    <h4 class="text-primary">
                        <?php if ($course['price'] > 0): ?>
                            <?php echo number_format($course['price']); ?> ريال
                        <?php else: ?>
                            مجانية
                        <?php endif; ?>
                    </h4>
                </div>
                
                <div class="mb-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <h6 class="text-primary"><?php echo $course['enrolled_students']; ?></h6>
                            <small class="text-muted">طالب مسجل</small>
                        </div>
                        <div class="col-6">
                            <h6 class="text-success"><?php echo $course['max_students'] - $course['enrolled_students']; ?></h6>
                            <small class="text-muted">مقعد متاح</small>
                        </div>
                    </div>
                    
                    <?php if ($course['max_students'] > 0): ?>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar" style="width: <?php echo ($course['enrolled_students'] / $course['max_students']) * 100; ?>%"></div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted d-block">
                        <i class="fas fa-calendar me-1"></i>
                        تاريخ البداية: <?php echo $course['start_date'] ? date('d/m/Y', strtotime($course['start_date'])) : 'غير محدد'; ?>
                    </small>
                    <?php if ($course['end_date']): ?>
                        <small class="text-muted d-block">
                            <i class="fas fa-calendar me-1"></i>
                            تاريخ النهاية: <?php echo date('d/m/Y', strtotime($course['end_date'])); ?>
                        </small>
                    <?php endif; ?>
                    <small class="text-muted d-block">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        المكان: <?php echo htmlspecialchars($course['location'] ?? 'غير محدد'); ?>
                    </small>
                </div>
                
                <?php if ($course['is_enrolled']): ?>
                    <div class="alert alert-success text-center">
                        <i class="fas fa-check-circle me-2"></i>
                        أنت مسجل في هذه الدورة
                    </div>
                    <a href="course-view.php?id=<?php echo $course['id']; ?>" class="btn btn-primary w-100">
                        <i class="fas fa-eye me-2"></i>مراجعة المحتوى
                    </a>
                <?php elseif ($course['enrolled_students'] >= $course['max_students']): ?>
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        الدورة مكتملة العدد
                    </div>
                <?php elseif ($course['start_date'] && strtotime($course['start_date']) < time()): ?>
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i>
                        الدورة بدأت بالفعل
                    </div>
                <?php else: ?>
                    <form method="POST">
                        <button type="submit" name="enroll" class="btn btn-success w-100">
                            <i class="fas fa-user-plus me-2"></i>التسجيل في الدورة
                        </button>
                    </form>
                <?php endif; ?>
                
                <div class="mt-3 text-center">
                    <a href="browse-courses.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-right me-2"></i>العودة للتصفح
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
