<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('admin');

// Get current month and year
$month = isset($_GET['month']) ? intval($_GET['month']) : date('n');
$year = isset($_GET['year']) ? intval($_GET['year']) : date('Y');

// Ensure valid month and year
if ($month < 1 || $month > 12) $month = date('n');
if ($year < 2020 || $year > 2030) $year = date('Y');

// Get sessions for this month
$start_date = "$year-" . str_pad($month, 2, '0', STR_PAD_LEFT) . "-01";
$end_date = date('Y-m-t', strtotime($start_date));

// Get sessions and courses for the calendar
$events_stmt = $conn->prepare("
    SELECT cs.id, cs.title, cs.session_date as event_date, cs.start_time, cs.end_time,
           cs.location, c.title as course_title, c.id as course_id,
           'session' as event_type, c.instructor_id,
           u.name as instructor_name,
           COUNT(DISTINCT e.user_id) as enrolled_students
    FROM course_sessions cs
    JOIN courses c ON cs.course_id = c.id
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN enrollments e ON c.id = e.course_id AND e.status IN ('active', 'completed')
    WHERE cs.session_date BETWEEN ? AND ?
    GROUP BY cs.id

    UNION ALL

    SELECT c.id, CONCAT('بداية دورة: ', c.title) as title, c.start_date as event_date,
           '09:00:00' as start_time, '17:00:00' as end_time,
           c.location, c.title as course_title, c.id as course_id,
           'course_start' as event_type, c.instructor_id,
           u.name as instructor_name,
           COUNT(DISTINCT e.user_id) as enrolled_students
    FROM courses c
    LEFT JOIN users u ON c.instructor_id = u.id
    LEFT JOIN enrollments e ON c.id = e.course_id AND e.status IN ('active', 'completed')
    WHERE c.start_date BETWEEN ? AND ? AND c.status = 'active'
    GROUP BY c.id

    ORDER BY event_date, start_time
");
$events_stmt->execute([$start_date, $end_date, $start_date, $end_date]);
$events = $events_stmt->fetchAll(PDO::FETCH_ASSOC);

// Group events by date for easier calendar rendering
$events_by_date = [];
foreach ($events as $event) {
    $date = $event['event_date'];
    if (!isset($events_by_date[$date])) {
        $events_by_date[$date] = [];
    }
    $events_by_date[$date][] = $event;
}



// Calendar helper functions
function getMonthName($month) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    return $months[$month];
}

function getDayName($day) {
    $days = [
        0 => 'الأحد', 1 => 'الاثنين', 2 => 'الثلاثاء', 3 => 'الأربعاء',
        4 => 'الخميس', 5 => 'الجمعة', 6 => 'السبت'
    ];
    return $days[$day];
}

$page_title = 'التقويم';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                إدارة الدورات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="enrollments.php">
                                <i class="fas fa-user-graduate"></i>
                                التسجيلات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="categories.php">
                                <i class="fas fa-tags"></i>
                                التصنيفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="calendar.php">
                                <i class="fas fa-calendar"></i>
                                التقويم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">التقويم</h1>
                    <a href="session-add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة جلسة
                    </a>
                </div>

                <!-- Calendar Navigation -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h4 class="mb-0"><?php echo getMonthName($month) . ' ' . $year; ?></h4>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group" role="group">
                                    <?php
                                    $prev_month = $month - 1;
                                    $prev_year = $year;
                                    if ($prev_month < 1) {
                                        $prev_month = 12;
                                        $prev_year--;
                                    }
                                    
                                    $next_month = $month + 1;
                                    $next_year = $year;
                                    if ($next_month > 12) {
                                        $next_month = 1;
                                        $next_year++;
                                    }
                                    ?>
                                    <a href="?month=<?php echo $prev_month; ?>&year=<?php echo $prev_year; ?>" 
                                       class="btn btn-outline-primary">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                    <a href="?month=<?php echo date('n'); ?>&year=<?php echo date('Y'); ?>" 
                                       class="btn btn-outline-primary">اليوم</a>
                                    <a href="?month=<?php echo $next_month; ?>&year=<?php echo $next_year; ?>" 
                                       class="btn btn-outline-primary">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Calendar Grid -->
                <div class="card">
                    <div class="card-body p-0">
                        <div class="calendar">
                            <div class="calendar-header">
                                <div class="row g-0">
                                    <?php
                                    $days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
                                    foreach ($days as $day): ?>
                                        <div class="col text-center py-3 fw-bold border-end">
                                            <?php echo $day; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <div class="calendar-body">
                                <?php
                                $first_day = date('w', strtotime($start_date));
                                $days_in_month = date('t', strtotime($start_date));
                                $current_date = date('Y-m-d');
                                
                                $week = 0;
                                $day = 1;
                                
                                for ($week = 0; $week < 6; $week++):
                                    if ($day > $days_in_month) break;
                                ?>
                                    <div class="row g-0">
                                        <?php for ($dow = 0; $dow < 7; $dow++): ?>
                                            <div class="col calendar-day border-end border-bottom" style="min-height: 120px;">
                                                <?php
                                                if (($week == 0 && $dow < $first_day) || $day > $days_in_month) {
                                                    echo '<div class="p-2"></div>';
                                                } else {
                                                    $current_day_date = sprintf('%04d-%02d-%02d', $year, $month, $day);
                                                    $is_today = $current_day_date == $current_date;
                                                    $has_events = isset($events_by_date[$current_day_date]);
                                                ?>
                                                    <div class="p-2 h-100">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <span class="fw-bold <?php echo $is_today ? 'text-primary' : ''; ?>">
                                                                <?php echo $day; ?>
                                                            </span>
                                                            <?php if ($is_today): ?>
                                                                <span class="badge bg-primary">اليوم</span>
                                                            <?php endif; ?>
                                                        </div>
                                                        
                                                        <?php if ($has_events): ?>
                                                            <?php foreach ($events_by_date[$current_day_date] as $index => $event): ?>
                                                                <?php if ($index < 2): // Show only first 2 events ?>
                                                                    <div class="mb-1">
                                                                        <div class="small <?php echo $event['event_type'] === 'course_start' ? 'bg-warning text-dark' : 'bg-primary text-white'; ?> px-2 py-1 rounded"
                                                                             style="font-size: 0.75rem; cursor: pointer;"
                                                                             onclick="showEventDetails(<?php echo $event['id']; ?>, '<?php echo $event['event_type']; ?>')">
                                                                            <div class="fw-bold">
                                                                                <?php echo date('H:i', strtotime($event['start_time'])); ?>
                                                                                <?php if ($event['event_type'] === 'course_start'): ?>
                                                                                    <i class="fas fa-star"></i>
                                                                                <?php endif; ?>
                                                                            </div>
                                                                            <div><?php echo htmlspecialchars(substr($event['title'], 0, 20)); ?></div>
                                                                        </div>
                                                                    </div>
                                                                <?php endif; ?>
                                                            <?php endforeach; ?>
                                                            <?php if (count($events_by_date[$current_day_date]) > 2): ?>
                                                                <div class="small text-muted" style="cursor: pointer;" onclick="showDayEvents('<?php echo $current_day_date; ?>')">
                                                                    +<?php echo count($events_by_date[$current_day_date]) - 2; ?> المزيد...
                                                                </div>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php
                                                    $day++;
                                                }
                                                ?>
                                            </div>
                                        <?php endfor; ?>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Sessions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock me-2"></i>
                            الجلسات القادمة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($sessions)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-2x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد جلسات مجدولة لهذا الشهر</p>
                                <a href="session-add.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>إضافة جلسة
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الوقت</th>
                                            <th>الدورة</th>
                                            <th>المدرب</th>
                                            <th>المكان</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($sessions as $session): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo formatDate($session['session_date'], 'Y-m-d'); ?></strong><br>
                                                    <small class="text-muted"><?php echo getDayName(date('w', strtotime($session['session_date']))); ?></small>
                                                </td>
                                                <td>
                                                    <?php echo date('H:i', strtotime($session['start_time'])); ?> - 
                                                    <?php echo date('H:i', strtotime($session['end_time'])); ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($session['course_title']); ?></td>
                                                <td><?php echo htmlspecialchars($session['instructor_name'] ?? 'غير محدد'); ?></td>
                                                <td><?php echo htmlspecialchars($session['location'] ?? 'غير محدد'); ?></td>
                                                <td>
                                                    <?php
                                                    $status_names = [
                                                        'scheduled' => 'مجدولة',
                                                        'ongoing' => 'جارية',
                                                        'completed' => 'مكتملة',
                                                        'cancelled' => 'ملغية'
                                                    ];
                                                    $status_classes = [
                                                        'scheduled' => 'bg-info',
                                                        'ongoing' => 'bg-warning',
                                                        'completed' => 'bg-success',
                                                        'cancelled' => 'bg-danger'
                                                    ];
                                                    ?>
                                                    <span class="badge <?php echo $status_classes[$session['status']] ?? 'bg-secondary'; ?>">
                                                        <?php echo $status_names[$session['status']] ?? $session['status']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="session-edit.php?id=<?php echo $session['id']; ?>" 
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="session-attendance.php?id=<?php echo $session['id']; ?>" 
                                                           class="btn btn-sm btn-outline-info">
                                                            <i class="fas fa-users"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<style>
.calendar {
    font-size: 0.9rem;
}

.calendar-day {
    transition: background-color 0.2s;
}

.calendar-day:hover {
    background-color: #f8f9fa;
}

.calendar-header .col {
    background-color: #0d6efd;
    color: white;
}

@media (max-width: 768px) {
    .calendar {
        font-size: 0.8rem;
    }
    
    .calendar-day {
        min-height: 80px !important;
    }
}

.event-session {
    background-color: #0d6efd !important;
}

.event-course_start {
    background-color: #ffc107 !important;
    color: #000 !important;
}
</style>

<!-- Event Details Modal -->
<div class="modal fade" id="eventDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الحدث</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="eventDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
function showEventDetails(eventId, eventType) {
    // Show loading
    document.getElementById('eventDetailsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('eventDetailsModal'));
    modal.show();

    // Load event details via AJAX
    fetch(`calendar-event-details.php?id=${eventId}&type=${eventType}`)
        .then(response => response.text())
        .then(data => {
            document.getElementById('eventDetailsContent').innerHTML = data;
        })
        .catch(error => {
            document.getElementById('eventDetailsContent').innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>';
        });
}

function showDayEvents(date) {
    // Show all events for a specific day
    document.getElementById('eventDetailsContent').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    const modal = new bootstrap.Modal(document.getElementById('eventDetailsModal'));
    modal.show();

    fetch(`calendar-day-events.php?date=${date}`)
        .then(response => response.text())
        .then(data => {
            document.getElementById('eventDetailsContent').innerHTML = data;
        })
        .catch(error => {
            document.getElementById('eventDetailsContent').innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>';
        });
}
</script>

<?php include '../includes/footer.php'; ?>