<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireRole('instructor');

$instructor_id = $_SESSION['user_id'];
$error = '';
$success = '';

// Handle session creation
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_session'])) {
    $course_id = (int)$_POST['course_id'];
    $title = sanitize_input($_POST['title']);
    $description = sanitize_input($_POST['description']);
    $session_date = $_POST['session_date'];
    $start_time = $_POST['start_time'];
    $end_time = $_POST['end_time'];
    $location = sanitize_input($_POST['location']);
    
    if (empty($title) || empty($session_date) || empty($start_time) || empty($end_time)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (strtotime($start_time) >= strtotime($end_time)) {
        $error = 'وقت البداية يجب أن يكون قبل وقت النهاية';
    } else {
        try {
            // Verify instructor owns the course
            $stmt = $conn->prepare("SELECT id FROM courses WHERE id = ? AND instructor_id = ?");
            $stmt->execute([$course_id, $instructor_id]);
            if (!$stmt->fetch()) {
                $error = 'غير مصرح لك بإنشاء جلسات لهذه الدورة';
            } else {
                $stmt = $conn->prepare("
                    INSERT INTO course_sessions (course_id, title, description, session_date, start_time, end_time, location) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([$course_id, $title, $description, $session_date, $start_time, $end_time, $location]);
                
                logActivity($instructor_id, 'session_create', "إنشاء جلسة: $title");
                $success = 'تم إنشاء الجلسة بنجاح';
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ في إنشاء الجلسة';
        }
    }
}

// Handle session deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $session_id = (int)$_GET['delete'];
    try {
        // Verify instructor owns the session
        $stmt = $conn->prepare("
            SELECT cs.title FROM course_sessions cs 
            JOIN courses c ON cs.course_id = c.id 
            WHERE cs.id = ? AND c.instructor_id = ?
        ");
        $stmt->execute([$session_id, $instructor_id]);
        $session = $stmt->fetch();
        
        if ($session) {
            $stmt = $conn->prepare("DELETE FROM course_sessions WHERE id = ?");
            $stmt->execute([$session_id]);
            
            logActivity($instructor_id, 'session_delete', "حذف جلسة: " . $session['title']);
            $success = 'تم حذف الجلسة بنجاح';
        } else {
            $error = 'غير مصرح لك بحذف هذه الجلسة';
        }
    } catch (PDOException $e) {
        $error = 'حدث خطأ في حذف الجلسة';
    }
}

// Get instructor's courses
$courses_stmt = $conn->prepare("
    SELECT id, title FROM courses 
    WHERE instructor_id = ? AND status = 'active'
    ORDER BY title
");
$courses_stmt->execute([$instructor_id]);
$courses = $courses_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get sessions with filters
$course_filter = isset($_GET['course']) ? (int)$_GET['course'] : 0;
$date_filter = isset($_GET['date']) ? $_GET['date'] : '';

$sessions_query = "
    SELECT cs.*, c.title as course_title,
           COUNT(a.id) as total_attendance,
           COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count
    FROM course_sessions cs
    JOIN courses c ON cs.course_id = c.id
    LEFT JOIN attendance a ON cs.id = a.session_id
    WHERE c.instructor_id = ?
";

$params = [$instructor_id];

if ($course_filter > 0) {
    $sessions_query .= " AND cs.course_id = ?";
    $params[] = $course_filter;
}

if (!empty($date_filter)) {
    $sessions_query .= " AND cs.session_date = ?";
    $params[] = $date_filter;
}

$sessions_query .= " GROUP BY cs.id ORDER BY cs.session_date DESC, cs.start_time DESC";

$sessions_stmt = $conn->prepare($sessions_query);
$sessions_stmt->execute($params);
$sessions = $sessions_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'إدارة الجلسات';
$base_url = '../';
$extra_css = ['../assets/css/dashboard.css'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-xl-2 p-0">
            <nav class="dashboard-sidebar">
                <div class="position-sticky">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="courses.php">
                                <i class="fas fa-graduation-cap"></i>
                                دوراتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="sessions.php">
                                <i class="fas fa-calendar-alt"></i>
                                الجلسات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate"></i>
                                الطلاب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="evaluations.php">
                                <i class="fas fa-clipboard-check"></i>
                                التقييمات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
        
        <div class="col-lg-9 col-xl-10">
            <main class="dashboard-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 fw-bold">إدارة الجلسات</h1>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSessionModal">
                        <i class="fas fa-plus me-2"></i>إنشاء جلسة جديدة
                    </button>
                </div>

                <?php if ($error): ?>
                    <?php echo showAlert($error, 'danger'); ?>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <?php echo showAlert($success, 'success'); ?>
                <?php endif; ?>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="course" class="form-label">الدورة</label>
                                <select class="form-select" id="course" name="course">
                                    <option value="">جميع الدورات</option>
                                    <?php foreach ($courses as $course): ?>
                                        <option value="<?php echo $course['id']; ?>" 
                                                <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($course['title']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="date" class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="date" name="date" value="<?php echo $date_filter; ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-2"></i>بحث
                                    </button>
                                    <a href="sessions.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Sessions List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الجلسات (<?php echo count($sessions); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($sessions)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h5>لا توجد جلسات</h5>
                                <p class="text-muted">لم تقم بإنشاء أي جلسات بعد</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSessionModal">
                                    <i class="fas fa-plus me-2"></i>إنشاء أول جلسة
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>العنوان</th>
                                            <th>الدورة</th>
                                            <th>التاريخ والوقت</th>
                                            <th>المكان</th>
                                            <th>الحضور</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($sessions as $session): ?>
                                            <?php
                                            $session_datetime = strtotime($session['session_date'] . ' ' . $session['start_time']);
                                            $is_past = $session_datetime < time();
                                            $is_today = date('Y-m-d') === $session['session_date'];
                                            $is_upcoming = $session_datetime > time();
                                            ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($session['title']); ?></strong>
                                                    <?php if ($session['description']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($session['description']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($session['course_title']); ?></td>
                                                <td>
                                                    <strong><?php echo date('d/m/Y', strtotime($session['session_date'])); ?></strong>
                                                    <br><small><?php echo date('H:i', strtotime($session['start_time'])); ?> - <?php echo date('H:i', strtotime($session['end_time'])); ?></small>
                                                </td>
                                                <td><?php echo htmlspecialchars($session['location'] ?? 'غير محدد'); ?></td>
                                                <td>
                                                    <?php if ($session['total_attendance'] > 0): ?>
                                                        <span class="badge bg-info">
                                                            <?php echo $session['present_count']; ?>/<?php echo $session['total_attendance']; ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">لا يوجد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($is_past): ?>
                                                        <span class="badge bg-secondary">منتهية</span>
                                                    <?php elseif ($is_today): ?>
                                                        <span class="badge bg-warning">اليوم</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-primary">قادمة</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="../admin/session-attendance.php?id=<?php echo $session['id']; ?>" 
                                                           class="btn btn-outline-primary" title="تسجيل الحضور">
                                                            <i class="fas fa-check"></i>
                                                        </a>
                                                        <a href="../admin/session-edit.php?id=<?php echo $session['id']; ?>" 
                                                           class="btn btn-outline-secondary" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="?delete=<?php echo $session['id']; ?>" 
                                                           class="btn btn-outline-danger" title="حذف"
                                                           onclick="return confirm('هل أنت متأكد من حذف هذه الجلسة؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Create Session Modal -->
<div class="modal fade" id="createSessionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء جلسة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="course_id" class="form-label">الدورة *</label>
                            <select class="form-select" id="course_id" name="course_id" required>
                                <option value="">اختر الدورة</option>
                                <?php foreach ($courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>">
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="title" class="form-label">عنوان الجلسة *</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الجلسة</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="session_date" class="form-label">تاريخ الجلسة *</label>
                            <input type="date" class="form-control" id="session_date" name="session_date" 
                                   min="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="start_time" class="form-label">وقت البداية *</label>
                            <input type="time" class="form-control" id="start_time" name="start_time" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="end_time" class="form-label">وقت النهاية *</label>
                            <input type="time" class="form-control" id="end_time" name="end_time" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="location" class="form-label">مكان الجلسة</label>
                        <input type="text" class="form-control" id="location" name="location" 
                               placeholder="مثال: قاعة 101، مختبر الحاسوب، عبر الإنترنت">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" name="create_session" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>إنشاء الجلسة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Validate end time is after start time
document.getElementById('end_time')?.addEventListener('change', function() {
    const startTime = document.getElementById('start_time').value;
    const endTime = this.value;
    
    if (startTime && endTime && startTime >= endTime) {
        this.setCustomValidity('وقت النهاية يجب أن يكون بعد وقت البداية');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('start_time')?.addEventListener('change', function() {
    const endTimeInput = document.getElementById('end_time');
    if (endTimeInput.value) {
        endTimeInput.dispatchEvent(new Event('change'));
    }
});
</script>

<?php include '../includes/footer.php'; ?>
