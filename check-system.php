<?php
/**
 * System Check Script
 * This script checks all components of the training platform
 */

// Prevent direct access in production
if (!isset($_GET['check']) || $_GET['check'] !== 'system') {
    die('Access denied');
}

$checks = [];
$overall_status = true;

// Check PHP version
$checks['php_version'] = [
    'name' => 'PHP Version',
    'status' => version_compare(PHP_VERSION, '8.0.0', '>='),
    'message' => 'PHP ' . PHP_VERSION . (version_compare(PHP_VERSION, '8.0.0', '>=') ? ' ✓' : ' (Required: 8.0+)'),
    'required' => true
];

// Check required PHP extensions
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'json', 'fileinfo'];
foreach ($required_extensions as $ext) {
    $checks["ext_$ext"] = [
        'name' => "PHP Extension: $ext",
        'status' => extension_loaded($ext),
        'message' => extension_loaded($ext) ? 'Loaded ✓' : 'Missing ✗',
        'required' => true
    ];
}

// Check database connection
try {
    require_once 'config/database.php';
    $checks['database'] = [
        'name' => 'Database Connection',
        'status' => true,
        'message' => 'Connected ✓',
        'required' => true
    ];
} catch (Exception $e) {
    $checks['database'] = [
        'name' => 'Database Connection',
        'status' => false,
        'message' => 'Failed: ' . $e->getMessage(),
        'required' => true
    ];
}

// Check file permissions
$directories = [
    'uploads/',
    'uploads/courses/',
    'uploads/materials/',
    'uploads/avatars/',
    'config/'
];

foreach ($directories as $dir) {
    $writable = is_writable($dir);
    $checks["perm_$dir"] = [
        'name' => "Directory: $dir",
        'status' => $writable,
        'message' => $writable ? 'Writable ✓' : 'Not writable ✗',
        'required' => true
    ];
}

// Check required files
$required_files = [
    'index.php',
    'config/database.php',
    'includes/functions.php',
    'includes/header.php',
    'includes/footer.php',
    'database/schema.sql',
    'assets/css/style.css',
    'assets/js/main.js'
];

foreach ($required_files as $file) {
    $exists = file_exists($file);
    $checks["file_$file"] = [
        'name' => "File: $file",
        'status' => $exists,
        'message' => $exists ? 'Exists ✓' : 'Missing ✗',
        'required' => true
    ];
}

// Check database tables
if (isset($conn)) {
    $required_tables = [
        'users', 'courses', 'categories', 'enrollments', 'course_sessions',
        'notifications', 'activity_logs', 'settings', 'permissions',
        'course_materials', 'evaluations', 'course_reviews', 'attendance'
    ];
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $conn->query("SELECT 1 FROM $table LIMIT 1");
            $checks["table_$table"] = [
                'name' => "Table: $table",
                'status' => true,
                'message' => 'Exists ✓',
                'required' => true
            ];
        } catch (PDOException $e) {
            $checks["table_$table"] = [
                'name' => "Table: $table",
                'status' => false,
                'message' => 'Missing or error ✗',
                'required' => true
            ];
        }
    }
}

// Check page accessibility
$pages = [
    'index.php' => 'Homepage',
    'about.php' => 'About Page',
    'courses.php' => 'Courses Page',
    'contact.php' => 'Contact Page',
    'auth/login.php' => 'Login Page',
    'auth/register.php' => 'Register Page',
    'sitemap.php' => 'Sitemap',
    'notifications.php' => 'Notifications'
];

foreach ($pages as $file => $name) {
    $accessible = file_exists($file) && is_readable($file);
    $checks["page_$file"] = [
        'name' => $name,
        'status' => $accessible,
        'message' => $accessible ? 'Accessible ✓' : 'Not accessible ✗',
        'required' => false
    ];
}

// Calculate overall status
foreach ($checks as $check) {
    if ($check['required'] && !$check['status']) {
        $overall_status = false;
        break;
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص النظام - منصة التدريب المهني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .check-item { padding: 0.5rem 0; border-bottom: 1px solid #eee; }
        .check-item:last-child { border-bottom: none; }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-header <?php echo $overall_status ? 'bg-success' : 'bg-danger'; ?> text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-<?php echo $overall_status ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                            فحص النظام - منصة التدريب المهني
                        </h3>
                        <p class="mb-0">
                            الحالة العامة: 
                            <strong><?php echo $overall_status ? 'جاهز للعمل' : 'يحتاج إصلاح'; ?></strong>
                        </p>
                    </div>
                    <div class="card-body">
                        <?php
                        $categories = [
                            'PHP Environment' => ['php_version', 'ext_pdo', 'ext_pdo_mysql', 'ext_mbstring', 'ext_openssl', 'ext_json', 'ext_fileinfo'],
                            'Database' => ['database'],
                            'File Permissions' => array_filter(array_keys($checks), function($k) { return strpos($k, 'perm_') === 0; }),
                            'Required Files' => array_filter(array_keys($checks), function($k) { return strpos($k, 'file_') === 0; }),
                            'Database Tables' => array_filter(array_keys($checks), function($k) { return strpos($k, 'table_') === 0; }),
                            'Page Accessibility' => array_filter(array_keys($checks), function($k) { return strpos($k, 'page_') === 0; })
                        ];
                        
                        foreach ($categories as $category => $check_keys):
                            if (empty($check_keys)) continue;
                        ?>
                            <h5 class="mt-4 mb-3">
                                <i class="fas fa-cog me-2"></i>
                                <?php echo $category; ?>
                            </h5>
                            <div class="row">
                                <?php foreach ($check_keys as $key): 
                                    if (!isset($checks[$key])) continue;
                                    $check = $checks[$key];
                                ?>
                                    <div class="col-md-6">
                                        <div class="check-item d-flex justify-content-between align-items-center">
                                            <span><?php echo htmlspecialchars($check['name']); ?></span>
                                            <span class="<?php echo $check['status'] ? 'status-good' : 'status-bad'; ?>">
                                                <?php echo htmlspecialchars($check['message']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endforeach; ?>
                        
                        <!-- System Information -->
                        <h5 class="mt-4 mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات النظام
                        </h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="check-item d-flex justify-content-between">
                                    <span>PHP Version</span>
                                    <span><?php echo PHP_VERSION; ?></span>
                                </div>
                                <div class="check-item d-flex justify-content-between">
                                    <span>Server Software</span>
                                    <span><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></span>
                                </div>
                                <div class="check-item d-flex justify-content-between">
                                    <span>Document Root</span>
                                    <span><?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="check-item d-flex justify-content-between">
                                    <span>Memory Limit</span>
                                    <span><?php echo ini_get('memory_limit'); ?></span>
                                </div>
                                <div class="check-item d-flex justify-content-between">
                                    <span>Upload Max Size</span>
                                    <span><?php echo ini_get('upload_max_filesize'); ?></span>
                                </div>
                                <div class="check-item d-flex justify-content-between">
                                    <span>Max Execution Time</span>
                                    <span><?php echo ini_get('max_execution_time'); ?>s</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recommendations -->
                        <?php if (!$overall_status): ?>
                            <div class="alert alert-danger mt-4">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>مشاكل تحتاج إصلاح:</h6>
                                <ul class="mb-0">
                                    <?php foreach ($checks as $check): ?>
                                        <?php if ($check['required'] && !$check['status']): ?>
                                            <li><?php echo htmlspecialchars($check['name'] . ': ' . $check['message']); ?></li>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-success mt-4">
                                <h6><i class="fas fa-check-circle me-2"></i>النظام جاهز للعمل!</h6>
                                <p class="mb-0">جميع المتطلبات الأساسية متوفرة والنظام يعمل بشكل صحيح.</p>
                            </div>
                        <?php endif; ?>
                        
                        <div class="text-center mt-4">
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-home me-2"></i>
                                الذهاب للصفحة الرئيسية
                            </a>
                            <a href="sitemap.php" class="btn btn-outline-secondary">
                                <i class="fas fa-sitemap me-2"></i>
                                خريطة الموقع
                            </a>
                            <button onclick="location.reload()" class="btn btn-outline-primary">
                                <i class="fas fa-sync-alt me-2"></i>
                                إعادة الفحص
                            </button>
                        </div>
                    </div>
                    <div class="card-footer text-muted text-center">
                        <small>
                            آخر فحص: <?php echo date('Y-m-d H:i:s'); ?> | 
                            منصة التدريب المهني v1.0.0
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
